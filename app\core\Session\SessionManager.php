<?php

namespace App\Core\Session;

use App\Core\Config\ConfigManager;
use Exception;

/**
 * Session Manager
 * High-performance session handling with security features
 */
class SessionManager
{
    private ConfigManager $config;
    private bool $started = false;
    private string $sessionId;
    private array $data = [];

    public function __construct(ConfigManager $config)
    {
        $this->config = $config;
    }

    /**
     * Start session
     */
    public function start(): bool
    {
        if ($this->started) {
            return true;
        }

        $this->configureSession();
        
        if (session_start()) {
            $this->started = true;
            $this->sessionId = session_id();
            $this->data = &$_SESSION;
            
            // Regenerate session ID periodically for security
            $this->regenerateIfNeeded();
            
            return true;
        }

        return false;
    }

    /**
     * Configure session settings
     */
    private function configureSession(): void
    {
        $config = $this->config->get('session', []);
        
        // Session cookie parameters
        session_set_cookie_params([
            'lifetime' => $config['lifetime'] * 60,
            'path' => $config['path'] ?? '/',
            'domain' => $config['domain'] ?? '',
            'secure' => $config['secure'] ?? false,
            'httponly' => $config['http_only'] ?? true,
            'samesite' => $config['same_site'] ?? 'Lax'
        ]);

        // Session name
        session_name($config['cookie'] ?? 'jobspace_session');

        // Session save path
        if ($config['driver'] === 'file') {
            $savePath = $config['files'] ?? BASE_PATH . '/storage/sessions';
            if (!is_dir($savePath)) {
                mkdir($savePath, 0755, true);
            }
            session_save_path($savePath);
        }

        // Session garbage collection
        ini_set('session.gc_probability', $config['lottery'][0] ?? 2);
        ini_set('session.gc_divisor', $config['lottery'][1] ?? 100);
        ini_set('session.gc_maxlifetime', $config['lifetime'] * 60);

        // Security settings
        ini_set('session.use_strict_mode', 1);
        ini_set('session.use_only_cookies', 1);
        ini_set('session.cookie_httponly', 1);
        ini_set('session.use_trans_sid', 0);
    }

    /**
     * Get session value
     */
    public function get(string $key, $default = null)
    {
        return $this->data[$key] ?? $default;
    }

    /**
     * Set session value
     */
    public function put(string $key, $value): void
    {
        $this->data[$key] = $value;
    }

    /**
     * Check if session key exists
     */
    public function has(string $key): bool
    {
        return isset($this->data[$key]);
    }

    /**
     * Remove session key
     */
    public function forget(string $key): void
    {
        unset($this->data[$key]);
    }

    /**
     * Get all session data
     */
    public function all(): array
    {
        return $this->data;
    }

    /**
     * Flash data for next request
     */
    public function flash(string $key, $value): void
    {
        $this->put("_flash.new.{$key}", $value);
    }

    /**
     * Get flash data
     */
    public function getFlash(string $key, $default = null)
    {
        return $this->get("_flash.old.{$key}", $default);
    }

    /**
     * Keep flash data for another request
     */
    public function keepFlash(array $keys = null): void
    {
        if ($keys === null) {
            $keys = array_keys($this->get('_flash.old', []));
        }

        foreach ($keys as $key) {
            $value = $this->get("_flash.old.{$key}");
            if ($value !== null) {
                $this->flash($key, $value);
            }
        }
    }

    /**
     * Get and forget value
     */
    public function pull(string $key, $default = null)
    {
        $value = $this->get($key, $default);
        $this->forget($key);
        return $value;
    }

    /**
     * Push value to array
     */
    public function push(string $key, $value): void
    {
        $array = $this->get($key, []);
        $array[] = $value;
        $this->put($key, $array);
    }

    /**
     * Increment numeric value
     */
    public function increment(string $key, int $value = 1): int
    {
        $current = (int) $this->get($key, 0);
        $new = $current + $value;
        $this->put($key, $new);
        return $new;
    }

    /**
     * Decrement numeric value
     */
    public function decrement(string $key, int $value = 1): int
    {
        return $this->increment($key, -$value);
    }

    /**
     * Regenerate session ID
     */
    public function regenerate(bool $deleteOld = true): bool
    {
        if (!$this->started) {
            return false;
        }

        if (session_regenerate_id($deleteOld)) {
            $this->sessionId = session_id();
            $this->put('_regenerated_at', time());
            return true;
        }

        return false;
    }

    /**
     * Regenerate session ID if needed
     */
    private function regenerateIfNeeded(): void
    {
        $lastRegeneration = $this->get('_regenerated_at', 0);
        $regenerateInterval = 1800; // 30 minutes

        if (time() - $lastRegeneration > $regenerateInterval) {
            $this->regenerate();
        }
    }

    /**
     * Destroy session
     */
    public function destroy(): bool
    {
        if (!$this->started) {
            return true;
        }

        $this->data = [];
        
        if (session_destroy()) {
            $this->started = false;
            $this->sessionId = '';
            
            // Clear session cookie
            $config = $this->config->get('session', []);
            setcookie(
                $config['cookie'] ?? 'jobspace_session',
                '',
                time() - 3600,
                $config['path'] ?? '/',
                $config['domain'] ?? '',
                $config['secure'] ?? false,
                $config['http_only'] ?? true
            );
            
            return true;
        }

        return false;
    }

    /**
     * Save session data
     */
    public function save(): void
    {
        if ($this->started) {
            $this->ageFlashData();
            session_write_close();
        }
    }

    /**
     * Age flash data
     */
    private function ageFlashData(): void
    {
        // Move new flash data to old
        $newFlash = $this->get('_flash.new', []);
        $this->put('_flash.old', $newFlash);
        $this->forget('_flash.new');
    }

    /**
     * Get session ID
     */
    public function getId(): string
    {
        return $this->sessionId;
    }

    /**
     * Set session ID
     */
    public function setId(string $id): void
    {
        if (!$this->started) {
            session_id($id);
            $this->sessionId = $id;
        }
    }

    /**
     * Check if session is started
     */
    public function isStarted(): bool
    {
        return $this->started;
    }

    /**
     * Get session name
     */
    public function getName(): string
    {
        return session_name();
    }

    /**
     * Set session name
     */
    public function setName(string $name): void
    {
        session_name($name);
    }

    /**
     * Clear all session data
     */
    public function flush(): void
    {
        $this->data = [];
    }

    /**
     * Get session token for CSRF protection
     */
    public function token(): string
    {
        $token = $this->get('_token');
        
        if (!$token) {
            $token = bin2hex(random_bytes(32));
            $this->put('_token', $token);
        }
        
        return $token;
    }

    /**
     * Regenerate CSRF token
     */
    public function regenerateToken(): string
    {
        $token = bin2hex(random_bytes(32));
        $this->put('_token', $token);
        return $token;
    }

    /**
     * Get session statistics
     */
    public function getStats(): array
    {
        return [
            'id' => $this->sessionId,
            'started' => $this->started,
            'data_count' => count($this->data),
            'data_size' => strlen(serialize($this->data)),
            'last_regenerated' => $this->get('_regenerated_at', 0)
        ];
    }

    /**
     * Validate session
     */
    public function validate(): bool
    {
        // Check if session is hijacked
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $storedUserAgent = $this->get('_user_agent', '');
        
        if ($storedUserAgent && $storedUserAgent !== $userAgent) {
            $this->destroy();
            return false;
        }
        
        if (!$storedUserAgent) {
            $this->put('_user_agent', $userAgent);
        }
        
        // Check IP address (optional, can be problematic with proxies)
        $ipCheck = $this->config->get('session.check_ip', false);
        if ($ipCheck) {
            $clientIp = $_SERVER['REMOTE_ADDR'] ?? '';
            $storedIp = $this->get('_ip_address', '');
            
            if ($storedIp && $storedIp !== $clientIp) {
                $this->destroy();
                return false;
            }
            
            if (!$storedIp) {
                $this->put('_ip_address', $clientIp);
            }
        }
        
        return true;
    }
}

<?php

namespace App\Core\Database;

use App\Core\Config\ConfigManager;
use PDO;
use PDOException;
use Exception;

/**
 * High-Performance Database Manager
 * Optimized for 50K+ concurrent connections
 */
class DatabaseManager
{
    private ConfigManager $config;
    private array $connections = [];
    private array $connectionPools = [];
    private QueryBuilder $queryBuilder;
    private int $maxConnections = 100;
    private int $connectionTimeout = 30;

    public function __construct(ConfigManager $config)
    {
        $this->config = $config;
        $this->queryBuilder = new QueryBuilder();
        $this->maxConnections = $config->get('database.max_connections', 100);
        $this->connectionTimeout = $config->get('database.timeout', 30);
    }

    /**
     * Get database connection
     */
    public function connection(string $name = 'default'): PDO
    {
        if (!isset($this->connections[$name])) {
            $this->connections[$name] = $this->createConnection($name);
        }

        return $this->connections[$name];
    }

    /**
     * Create new database connection
     */
    private function createConnection(string $name): PDO
    {
        $config = $this->config->get("database.connections.{$name}");
        
        if (!$config) {
            throw new Exception("Database connection [{$name}] not configured");
        }

        $dsn = $this->buildDsn($config);
        $options = $this->getConnectionOptions($config);

        try {
            $pdo = new PDO($dsn, $config['username'], $config['password'], $options);
            
            // Set connection attributes for performance
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
            $pdo->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);
            
            // MySQL specific optimizations
            if ($config['driver'] === 'mysql') {
                $pdo->exec("SET SESSION sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'");
                $pdo->exec("SET SESSION time_zone = '+00:00'");
                $pdo->exec("SET SESSION innodb_lock_wait_timeout = 5");
            }

            return $pdo;

        } catch (PDOException $e) {
            throw new Exception("Database connection failed: " . $e->getMessage());
        }
    }

    /**
     * Build DSN string
     */
    private function buildDsn(array $config): string
    {
        $driver = $config['driver'];
        $host = $config['host'];
        $port = $config['port'] ?? 3306;
        $database = $config['database'];
        $charset = $config['charset'] ?? 'utf8mb4';

        switch ($driver) {
            case 'mysql':
                return "mysql:host={$host};port={$port};dbname={$database};charset={$charset}";
            case 'pgsql':
                return "pgsql:host={$host};port={$port};dbname={$database}";
            case 'sqlite':
                return "sqlite:{$database}";
            default:
                throw new Exception("Unsupported database driver: {$driver}");
        }
    }

    /**
     * Get connection options
     */
    private function getConnectionOptions(array $config): array
    {
        return [
            PDO::ATTR_TIMEOUT => $this->connectionTimeout,
            PDO::ATTR_PERSISTENT => $config['persistent'] ?? false,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$config['charset']} COLLATE {$config['collation']}",
            PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true,
            PDO::MYSQL_ATTR_FOUND_ROWS => true,
        ];
    }

    /**
     * Execute query with prepared statement
     */
    public function query(string $sql, array $bindings = [], string $connection = 'default'): array
    {
        $pdo = $this->connection($connection);
        
        try {
            $statement = $pdo->prepare($sql);
            $statement->execute($bindings);
            
            return $statement->fetchAll();
            
        } catch (PDOException $e) {
            throw new Exception("Query execution failed: " . $e->getMessage());
        }
    }

    /**
     * Execute insert query
     */
    public function insert(string $table, array $data, string $connection = 'default'): int
    {
        $sql = $this->queryBuilder->insert($table, $data);
        $pdo = $this->connection($connection);
        
        try {
            $statement = $pdo->prepare($sql);
            $statement->execute(array_values($data));
            
            return (int) $pdo->lastInsertId();
            
        } catch (PDOException $e) {
            throw new Exception("Insert failed: " . $e->getMessage());
        }
    }

    /**
     * Execute update query
     */
    public function update(string $table, array $data, array $where, string $connection = 'default'): int
    {
        $sql = $this->queryBuilder->update($table, $data, $where);
        $pdo = $this->connection($connection);
        
        try {
            $bindings = array_merge(array_values($data), array_values($where));
            $statement = $pdo->prepare($sql);
            $statement->execute($bindings);
            
            return $statement->rowCount();
            
        } catch (PDOException $e) {
            throw new Exception("Update failed: " . $e->getMessage());
        }
    }

    /**
     * Execute delete query
     */
    public function delete(string $table, array $where, string $connection = 'default'): int
    {
        $sql = $this->queryBuilder->delete($table, $where);
        $pdo = $this->connection($connection);
        
        try {
            $statement = $pdo->prepare($sql);
            $statement->execute(array_values($where));
            
            return $statement->rowCount();
            
        } catch (PDOException $e) {
            throw new Exception("Delete failed: " . $e->getMessage());
        }
    }

    /**
     * Execute select query
     */
    public function select(string $table, array $columns = ['*'], array $where = [], string $connection = 'default'): array
    {
        $sql = $this->queryBuilder->select($table, $columns, $where);
        return $this->query($sql, array_values($where), $connection);
    }

    /**
     * Find single record
     */
    public function find(string $table, $id, string $primaryKey = 'id', string $connection = 'default'): ?array
    {
        $result = $this->select($table, ['*'], [$primaryKey => $id], $connection);
        return $result[0] ?? null;
    }

    /**
     * Begin transaction
     */
    public function beginTransaction(string $connection = 'default'): void
    {
        $this->connection($connection)->beginTransaction();
    }

    /**
     * Commit transaction
     */
    public function commit(string $connection = 'default'): void
    {
        $this->connection($connection)->commit();
    }

    /**
     * Rollback transaction
     */
    public function rollback(string $connection = 'default'): void
    {
        $this->connection($connection)->rollBack();
    }

    /**
     * Execute transaction with callback
     */
    public function transaction(callable $callback, string $connection = 'default')
    {
        $this->beginTransaction($connection);
        
        try {
            $result = $callback($this);
            $this->commit($connection);
            return $result;
            
        } catch (Exception $e) {
            $this->rollback($connection);
            throw $e;
        }
    }

    /**
     * Get query builder
     */
    public function table(string $table): QueryBuilder
    {
        return $this->queryBuilder->table($table)->setConnection($this);
    }

    /**
     * Execute raw SQL
     */
    public function raw(string $sql, array $bindings = [], string $connection = 'default')
    {
        return $this->query($sql, $bindings, $connection);
    }

    /**
     * Get last insert ID
     */
    public function lastInsertId(string $connection = 'default'): string
    {
        return $this->connection($connection)->lastInsertId();
    }

    /**
     * Check if table exists
     */
    public function tableExists(string $table, string $connection = 'default'): bool
    {
        $sql = "SHOW TABLES LIKE ?";
        $result = $this->query($sql, [$table], $connection);
        return !empty($result);
    }

    /**
     * Get table columns
     */
    public function getColumns(string $table, string $connection = 'default'): array
    {
        $sql = "DESCRIBE {$table}";
        return $this->query($sql, [], $connection);
    }

    /**
     * Disconnect all connections
     */
    public function disconnect(): void
    {
        $this->connections = [];
        $this->connectionPools = [];
    }

    /**
     * Get connection statistics
     */
    public function getStats(): array
    {
        return [
            'active_connections' => count($this->connections),
            'max_connections' => $this->maxConnections,
            'connection_timeout' => $this->connectionTimeout
        ];
    }

    /**
     * Ping connection to keep it alive
     */
    public function ping(string $connection = 'default'): bool
    {
        try {
            $this->connection($connection)->query('SELECT 1');
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
}

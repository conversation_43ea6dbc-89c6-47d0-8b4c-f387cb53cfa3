<?php

define('BASE_PATH', __DIR__);

// Load environment variables
if (file_exists(BASE_PATH . '/.env')) {
    $lines = file(BASE_PATH . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

// Load autoloader
require_once BASE_PATH . '/vendor/autoload.php';
require_once BASE_PATH . '/app/core/Database.php';

use App\Core\Database;

echo "🔍 Testing Database Connection...\n";

try {
    // Test connection
    $connection = Database::getConnection();
    echo "✅ Database connection successful!\n";
    
    // Test table existence
    $tables = ['users', 'verification_tokens', 'sessions', 'login_attempts'];
    
    foreach ($tables as $table) {
        if (Database::tableExists($table)) {
            echo "✅ Table '$table' exists\n";
            
            // Count records
            $result = Database::fetch("SELECT COUNT(*) as count FROM $table");
            echo "   Records: " . ($result['count'] ?? 0) . "\n";
        } else {
            echo "❌ Table '$table' does not exist\n";
        }
    }
    
    // Test email availability check
    echo "\n🔍 Testing User Model...\n";
    
    require_once BASE_PATH . '/app/modules/auth/models/User.php';
    use App\Modules\Auth\Models\User;
    
    $testEmail = '<EMAIL>';
    $isAvailable = User::isEmailAvailable($testEmail);
    echo "Email '$testEmail' available: " . ($isAvailable ? 'Yes' : 'No') . "\n";
    
    $testUsername = 'testuser';
    $isAvailable = User::isUsernameAvailable($testUsername);
    echo "Username '$testUsername' available: " . ($isAvailable ? 'Yes' : 'No') . "\n";
    
    echo "\n🎉 All tests passed!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>

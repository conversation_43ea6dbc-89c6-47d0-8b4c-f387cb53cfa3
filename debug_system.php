<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>JobSpace System Debug</h1>";

// 1. Check database
echo "<h2>1. Database Check</h2>";
try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=jobspace', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Database connected<br>";
    
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "Tables: " . implode(', ', $tables) . "<br>";
    
    // Create verification_tokens if not exists
    if (!in_array('verification_tokens', $tables)) {
        echo "Creating verification_tokens table...<br>";
        $sql = "CREATE TABLE verification_tokens (
            id INT AUTO_INCREMENT PRIMARY KEY,
            email VARCHAR(255) NOT NULL,
            token VARCHAR(255) NOT NULL,
            otp VARCHAR(6) NOT NULL,
            user_data LONGTEXT NOT NULL,
            expires_at TIMESTAMP NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_email (email)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
        $pdo->exec($sql);
        echo "✅ verification_tokens table created<br>";
    }
    
    // Check users table columns
    if (in_array('users', $tables)) {
        $stmt = $pdo->query("DESCRIBE users");
        $columns = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $columns[] = $row['Field'];
        }
        
        // Add missing columns
        if (!in_array('email_verified', $columns)) {
            $pdo->exec("ALTER TABLE users ADD COLUMN email_verified BOOLEAN DEFAULT FALSE");
            echo "✅ Added email_verified column<br>";
        }
        
        if (!in_array('referral_code_generated', $columns)) {
            $pdo->exec("ALTER TABLE users ADD COLUMN referral_code_generated VARCHAR(50) NULL");
            echo "✅ Added referral_code_generated column<br>";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

// 2. Check routing
echo "<h2>2. Routing Check</h2>";
$currentUrl = $_SERVER['REQUEST_URI'];
echo "Current URL: $currentUrl<br>";

// Check if auth routes exist
$authRoutes = [
    '/jobspace/auth/register',
    '/jobspace/auth/login',
    '/jobspace/auth/verify-otp'
];

foreach ($authRoutes as $route) {
    echo "<a href='$route' target='_blank'>Test: $route</a><br>";
}

// 3. Check files
echo "<h2>3. File Structure Check</h2>";
$filesToCheck = [
    'app/modules/auth/controllers/AuthController.php',
    'app/modules/auth/services/AuthService.php',
    'app/modules/auth/services/EmailService.php',
    'app/modules/auth/models/User.php',
    'app/modules/auth/routes/web.php',
    'app/modules/auth/views/pages/register.php',
    'app/modules/auth/views/pages/verify-otp.php'
];

foreach ($filesToCheck as $file) {
    if (file_exists($file)) {
        echo "✅ $file exists<br>";
    } else {
        echo "❌ $file missing<br>";
    }
}

// 4. Test registration process
echo "<h2>4. Test Registration Process</h2>";
echo "<form method='POST' action='/jobspace/auth/process-step1'>
    <input type='hidden' name='csrf_token' value='" . (\App\Core\Security::getCSRFToken() ?? 'test') . "'>
    <input type='text' name='first_name' placeholder='First Name' value='Test' required><br><br>
    <input type='text' name='last_name' placeholder='Last Name' value='User' required><br><br>
    <input type='email' name='email' placeholder='Email' value='<EMAIL>' required><br><br>
    <input type='tel' name='phone' placeholder='Phone' value='01700000000' required><br><br>
    <input type='date' name='date_of_birth' value='1990-01-01' required><br><br>
    <select name='gender' required>
        <option value='Male'>Male</option>
        <option value='Female'>Female</option>
    </select><br><br>
    <input type='password' name='password' placeholder='Password' value='password123' required><br><br>
    <input type='password' name='confirm_password' placeholder='Confirm Password' value='password123' required><br><br>
    <button type='submit'>Test Registration Step 1</button>
</form>";

echo "<h2>5. Manual Test Links</h2>";
echo "<a href='/jobspace/auth/register'>Registration Page</a><br>";
echo "<a href='/jobspace/'>Home Page</a><br>";
echo "<a href='/jobspace/test'>Test Route</a><br>";
?>

<?php

/**
 * JobSpace Setup Script
 * Creates database tables and initial configuration
 */

define('BASE_PATH', __DIR__);

// Load autoloader
require_once BASE_PATH . '/vendor/autoload.php';

// Load environment variables
if (file_exists(BASE_PATH . '/.env')) {
    $lines = file(BASE_PATH . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

// Load core classes
require_once BASE_PATH . '/app/core/Database.php';

use App\Core\Database;

echo "🚀 JobSpace Setup Script\n";
echo "========================\n\n";

try {
    echo "📊 Creating database tables...\n";
    
    // Create tables
    Database::createTables();
    
    echo "✅ Database tables created successfully!\n\n";
    
    // Check if admin user exists
    echo "👤 Checking for admin user...\n";
    
    $adminExists = Database::fetch("SELECT COUNT(*) as count FROM users WHERE role = 'Admin'");
    
    if (($adminExists['count'] ?? 0) == 0) {
        echo "⚠️  No admin user found. You can create one during registration.\n";
    } else {
        echo "✅ Admin user already exists.\n";
    }
    
    echo "\n📋 Setup Summary:\n";
    echo "================\n";
    echo "✅ Database tables created\n";
    echo "✅ Security features enabled\n";
    echo "✅ Session management configured\n";
    echo "✅ Email system ready\n";
    echo "✅ Rate limiting active\n";
    echo "✅ CSRF protection enabled\n\n";
    
    echo "🌐 Your JobSpace installation is ready!\n";
    echo "Visit: http://localhost/jobspace/\n\n";
    
    echo "📧 Email Configuration:\n";
    echo "======================\n";
    if (empty($_ENV['MAIL_USERNAME']) || empty($_ENV['MAIL_PASSWORD'])) {
        echo "⚠️  Email not configured. Update .env file with your email settings:\n";
        echo "   MAIL_USERNAME=<EMAIL>\n";
        echo "   MAIL_PASSWORD=your-app-password\n";
        echo "   (Emails will be logged to error log until configured)\n\n";
    } else {
        echo "✅ Email configured: " . $_ENV['MAIL_USERNAME'] . "\n\n";
    }
    
    echo "🔐 Security Features:\n";
    echo "====================\n";
    echo "✅ CSRF Protection: Enabled\n";
    echo "✅ Rate Limiting: Enabled\n";
    echo "✅ Session Security: Enabled\n";
    echo "✅ Password Hashing: bcrypt\n";
    echo "✅ SQL Injection Protection: PDO Prepared Statements\n";
    echo "✅ XSS Protection: Input Sanitization\n\n";
    
    echo "📱 Available Features:\n";
    echo "=====================\n";
    echo "✅ Multi-step Registration\n";
    echo "✅ Email + OTP Verification\n";
    echo "✅ Password Reset\n";
    echo "✅ Role-based Access\n";
    echo "✅ Profile Management\n";
    echo "✅ Referral System\n";
    echo "✅ File Upload Support\n\n";
    
    echo "🎯 Next Steps:\n";
    echo "=============\n";
    echo "1. Configure email settings in .env file\n";
    echo "2. Test registration process\n";
    echo "3. Create admin account\n";
    echo "4. Explore the platform\n\n";
    
    echo "🆘 Need Help?\n";
    echo "=============\n";
    echo "- Check error logs for any issues\n";
    echo "- Ensure database credentials are correct\n";
    echo "- Verify file permissions\n";
    echo "- Test email configuration\n\n";
    
} catch (Exception $e) {
    echo "❌ Setup failed: " . $e->getMessage() . "\n\n";
    
    echo "🔧 Troubleshooting:\n";
    echo "==================\n";
    echo "1. Check database connection in .env file\n";
    echo "2. Ensure MySQL/MariaDB is running\n";
    echo "3. Verify database exists: " . ($_ENV['DB_DATABASE'] ?? 'jobspace') . "\n";
    echo "4. Check database user permissions\n\n";
    
    echo "📋 Database Configuration:\n";
    echo "=========================\n";
    echo "Host: " . ($_ENV['DB_HOST'] ?? 'localhost') . "\n";
    echo "Port: " . ($_ENV['DB_PORT'] ?? '3306') . "\n";
    echo "Database: " . ($_ENV['DB_DATABASE'] ?? 'jobspace') . "\n";
    echo "Username: " . ($_ENV['DB_USERNAME'] ?? 'root') . "\n\n";
    
    exit(1);
}

echo "🎉 Setup completed successfully!\n";
echo "Happy coding with JobSpace! 🚀\n";

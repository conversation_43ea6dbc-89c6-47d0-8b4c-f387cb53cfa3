<?php

namespace App\Modules\Auth\Models;

use App\Core\Database;

class User
{
    private static string $tableName = 'users';
    
    /**
     * User registration data structure
     */
    public static function getRegistrationFields(): array
    {
        return [
            // Step 1: Personal Information
            'step1' => [
                'first_name' => ['type' => 'text', 'required' => true, 'max' => 50],
                'last_name' => ['type' => 'text', 'required' => true, 'max' => 50],
                'email' => ['type' => 'email', 'required' => true, 'unique' => true],
                'phone' => ['type' => 'tel', 'required' => true, 'unique' => true],
                'date_of_birth' => ['type' => 'date', 'required' => true],
                'gender' => ['type' => 'select', 'required' => true, 'options' => ['Male', 'Female', 'Other']],
                'password' => ['type' => 'password', 'required' => true, 'min' => 8],
                'confirm_password' => ['type' => 'password', 'required' => true, 'match' => 'password']
            ],
            
            // Step 2: Login Info & Additional Details
            'step2' => [
                'username' => ['type' => 'text', 'required' => true, 'unique' => true, 'min' => 3, 'max' => 30],
                'address' => ['type' => 'textarea', 'required' => false, 'max' => 500],
                'role' => ['type' => 'select', 'required' => true, 'options' => ['User', 'Business', 'Creator']],
                'profile_picture' => ['type' => 'file', 'required' => false, 'accept' => 'image/*'],
                'referral_code' => ['type' => 'text', 'required' => false, 'max' => 20],
                'terms_accepted' => ['type' => 'checkbox', 'required' => true]
            ]
        ];
    }
    
    /**
     * Get user roles with admin check
     */
    public static function getAvailableRoles(): array
    {
        $roles = ['User', 'Business', 'Creator'];
        
        // Add Admin role if no admin exists
        if (!self::adminExists()) {
            $roles[] = 'Admin';
        }
        
        return $roles;
    }
    
    /**
     * Check if admin user exists
     */
    public static function adminExists(): bool
    {
        try {
            $result = Database::fetch(
                "SELECT COUNT(*) as count FROM " . self::$tableName . " WHERE role = 'Admin'",
                []
            );
            return ($result['count'] ?? 0) > 0;
        } catch (\Exception $e) {
            return false; // If table doesn't exist yet, allow admin creation
        }
    }

    /**
     * Validate email availability
     */
    public static function isEmailAvailable(string $email): bool
    {
        try {
            $result = Database::fetch(
                "SELECT id FROM " . self::$tableName . " WHERE email = ?",
                [$email]
            );
            return $result === null;
        } catch (\Exception $e) {
            return true; // If table doesn't exist yet, allow registration
        }
    }

    /**
     * Validate username availability
     */
    public static function isUsernameAvailable(string $username): bool
    {
        try {
            $result = Database::fetch(
                "SELECT id FROM " . self::$tableName . " WHERE username = ?",
                [$username]
            );
            return $result === null;
        } catch (\Exception $e) {
            return true; // If table doesn't exist yet, allow registration
        }
    }
    
    /**
     * Generate verification token
     */
    public static function generateVerificationToken(): string
    {
        return bin2hex(random_bytes(32));
    }
    
    /**
     * Generate OTP
     */
    public static function generateOTP(int $length = 6): string
    {
        $otp = '';
        for ($i = 0; $i < $length; $i++) {
            $otp .= random_int(0, 9);
        }
        return $otp;
    }
    
    /**
     * Hash password
     */
    public static function hashPassword(string $password): string
    {
        return password_hash($password, PASSWORD_DEFAULT);
    }
    
    /**
     * Verify password
     */
    public static function verifyPassword(string $password, string $hash): bool
    {
        return password_verify($password, $hash);
    }
    
    /**
     * Generate referral code
     */
    public static function generateReferralCode(string $username): string
    {
        return strtoupper(substr($username, 0, 3) . random_int(1000, 9999));
    }
    
    /**
     * Validate referral code
     */
    public static function isValidReferralCode(string $code): bool
    {
        // Database check would go here
        return !empty($code);
    }
    
    /**
     * Get user by email
     */
    public static function findByEmail(string $email): ?array
    {
        try {
            return Database::fetch(
                "SELECT * FROM " . self::$tableName . " WHERE email = ?",
                [$email]
            );
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Get user by username
     */
    public static function findByUsername(string $username): ?array
    {
        try {
            return Database::fetch(
                "SELECT * FROM " . self::$tableName . " WHERE username = ?",
                [$username]
            );
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Create new user
     */
    public static function create(array $userData): bool
    {
        try {
            $userData['created_at'] = date('Y-m-d H:i:s');
            $userData['updated_at'] = date('Y-m-d H:i:s');

            $userId = Database::insert(self::$tableName, $userData);
            return $userId > 0;
        } catch (\Exception $e) {
            error_log("User creation failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update user verification status
     */
    public static function markAsVerified(string $email): bool
    {
        try {
            $rowsAffected = Database::update(
                self::$tableName,
                [
                    'email_verified' => true,
                    'email_verified_at' => date('Y-m-d H:i:s'),
                    'status' => 'active',
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                'email = :email',
                ['email' => $email]
            );
            return $rowsAffected > 0;
        } catch (\Exception $e) {
            error_log("User verification failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Store verification data
     */
    public static function storeVerificationData(string $email, array $data): bool
    {
        try {
            // Clean up old verification data for this email
            Database::delete('verification_tokens', 'email = ?', [$email]);

            // Insert new verification data
            $verificationData = [
                'email' => $email,
                'token' => $data['verification_token'],
                'otp' => $data['otp'],
                'user_data' => json_encode($data['user_data']),
                'expires_at' => date('Y-m-d H:i:s', $data['otp_expires']),
                'created_at' => date('Y-m-d H:i:s')
            ];

            $id = Database::insert('verification_tokens', $verificationData);
            return $id > 0;
        } catch (\Exception $e) {
            error_log("Verification data storage failed: " . $e->getMessage());
            // Fallback to session storage
            $_SESSION['verification_data'][$email] = $data;
            return true;
        }
    }

    /**
     * Get verification data
     */
    public static function getVerificationData(string $email): ?array
    {
        try {
            $result = Database::fetch(
                "SELECT * FROM verification_tokens WHERE email = ? AND expires_at > NOW() ORDER BY created_at DESC LIMIT 1",
                [$email]
            );

            if ($result) {
                return [
                    'verification_token' => $result['token'],
                    'otp' => $result['otp'],
                    'otp_expires' => strtotime($result['expires_at']),
                    'user_data' => json_decode($result['user_data'], true),
                    'created_at' => strtotime($result['created_at'])
                ];
            }

            return null;
        } catch (\Exception $e) {
            error_log("Verification data retrieval failed: " . $e->getMessage());
            // Fallback to session storage
            return $_SESSION['verification_data'][$email] ?? null;
        }
    }

    /**
     * Clean up verification data
     */
    public static function cleanupVerificationData(string $email): bool
    {
        try {
            Database::delete('verification_tokens', 'email = ?', [$email]);
            unset($_SESSION['verification_data'][$email]);
            return true;
        } catch (\Exception $e) {
            error_log("Verification data cleanup failed: " . $e->getMessage());
            unset($_SESSION['verification_data'][$email]);
            return false;
        }
    }

    /**
     * Update last login time
     */
    public static function updateLastLogin(int $userId): bool
    {
        try {
            $rowsAffected = Database::update(
                self::$tableName,
                [
                    'last_login' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                'id = :id',
                ['id' => $userId]
            );
            return $rowsAffected > 0;
        } catch (\Exception $e) {
            error_log("Last login update failed: " . $e->getMessage());
            return false;
        }
    }



}

<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

define('BASE_PATH', __DIR__);

// Load environment variables
if (file_exists(BASE_PATH . '/.env')) {
    $lines = file(BASE_PATH . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

// Load autoloader
require_once BASE_PATH . '/vendor/autoload.php';
require_once BASE_PATH . '/app/core/Database.php';
require_once BASE_PATH . '/app/core/SessionManager.php';
require_once BASE_PATH . '/app/core/Security.php';

use App\Core\Database;
use App\Core\SessionManager;
use App\Core\Security;

echo "<h1>Registration Test</h1>";

// Initialize session
SessionManager::init();

echo "<h2>Step 1: Database Connection</h2>";
try {
    $connection = Database::getConnection();
    echo "✅ Database connected<br>";
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
    exit;
}

echo "<h2>Step 2: Test User Model</h2>";
require_once BASE_PATH . '/app/modules/auth/models/User.php';
use App\Modules\Auth\Models\User;

$testEmail = '<EMAIL>';
$isAvailable = User::isEmailAvailable($testEmail);
echo "Email '$testEmail' available: " . ($isAvailable ? 'Yes' : 'No') . "<br>";

echo "<h2>Step 3: Test Registration Data</h2>";
$testData = [
    'first_name' => 'John',
    'last_name' => 'Doe',
    'email' => '<EMAIL>',
    'phone' => '+8801234567890',
    'date_of_birth' => '1990-01-01',
    'gender' => 'Male',
    'password' => 'Test123456'
];

echo "Test data prepared<br>";

echo "<h2>Step 4: Test AuthService</h2>";
require_once BASE_PATH . '/app/modules/auth/services/AuthService.php';
use App\Modules\Auth\Services\AuthService;

$authService = new AuthService();
$errors = $authService->validateStep1($testData);

if (empty($errors)) {
    echo "✅ Validation passed<br>";
} else {
    echo "❌ Validation errors:<br>";
    foreach ($errors as $field => $error) {
        echo "- $field: $error<br>";
    }
}

echo "<h2>Step 5: Test Password Hashing</h2>";
$hashedPassword = Security::hashPassword($testData['password']);
echo "Password hashed: " . substr($hashedPassword, 0, 20) . "...<br>";

echo "<h2>Step 6: Test OTP Generation</h2>";
$otp = User::generateOTP();
echo "Generated OTP: $otp<br>";

echo "<h2>Step 7: Test Verification Token</h2>";
$token = User::generateVerificationToken();
echo "Generated token: " . substr($token, 0, 20) . "...<br>";

echo "<h2>✅ All tests completed!</h2>";
echo "<p>If all steps show ✅, the registration system should work.</p>";
?>

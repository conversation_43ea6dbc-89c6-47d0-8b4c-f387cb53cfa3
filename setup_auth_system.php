<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Setting up Authentication System</h1>";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=jobspace', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Database connected<br>";
    
    // 1. Create verification_tokens table
    echo "<h2>1. Creating verification_tokens table</h2>";
    $sql = "
    CREATE TABLE IF NOT EXISTS verification_tokens (
        id INT AUTO_INCREMENT PRIMARY KEY,
        email VARCHAR(255) NOT NULL,
        token VARCHAR(255) NOT NULL,
        otp VARCHAR(6) NOT NULL,
        user_data LONGTEXT NOT NULL,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_email (email),
        INDEX idx_token (token),
        INDEX idx_expires_at (expires_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ";
    $pdo->exec($sql);
    echo "✅ verification_tokens table created<br>";
    
    // 2. Check and update users table structure
    echo "<h2>2. Updating users table structure</h2>";
    $stmt = $pdo->query("DESCRIBE users");
    $columns = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $columns[] = $row['Field'];
    }
    
    // Add missing columns
    $columnsToAdd = [
        'email_verified' => "ALTER TABLE users ADD COLUMN email_verified BOOLEAN DEFAULT FALSE",
        'referral_code_generated' => "ALTER TABLE users ADD COLUMN referral_code_generated VARCHAR(50) NULL",
        'referral_code_used' => "ALTER TABLE users ADD COLUMN referral_code_used VARCHAR(50) NULL",
        'address' => "ALTER TABLE users ADD COLUMN address TEXT NULL",
        'profile_picture' => "ALTER TABLE users ADD COLUMN profile_picture VARCHAR(500) NULL"
    ];
    
    foreach ($columnsToAdd as $column => $sql) {
        if (!in_array($column, $columns)) {
            echo "Adding column: $column<br>";
            $pdo->exec($sql);
            echo "✅ Column $column added<br>";
        } else {
            echo "✅ Column $column already exists<br>";
        }
    }
    
    // 3. Test database operations
    echo "<h2>3. Testing database operations</h2>";
    
    // Test verification_tokens insert
    $testData = [
        'email' => '<EMAIL>',
        'token' => 'test_token_' . time(),
        'otp' => '123456',
        'user_data' => json_encode(['test' => 'data']),
        'expires_at' => date('Y-m-d H:i:s', time() + 600)
    ];
    
    $stmt = $pdo->prepare("
        INSERT INTO verification_tokens (email, token, otp, user_data, expires_at) 
        VALUES (?, ?, ?, ?, ?)
    ");
    $stmt->execute([
        $testData['email'],
        $testData['token'],
        $testData['otp'],
        $testData['user_data'],
        $testData['expires_at']
    ]);
    echo "✅ Test verification data inserted<br>";
    
    // Test verification_tokens select
    $stmt = $pdo->prepare("SELECT * FROM verification_tokens WHERE email = ?");
    $stmt->execute([$testData['email']]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($result) {
        echo "✅ Test verification data retrieved<br>";
    }
    
    // Clean up test data
    $pdo->exec("DELETE FROM verification_tokens WHERE email = '<EMAIL>'");
    echo "✅ Test data cleaned up<br>";
    
    // 4. Create uploads directory
    echo "<h2>4. Creating uploads directory</h2>";
    $uploadDir = 'public/uploads/profiles';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
        echo "✅ Uploads directory created: $uploadDir<br>";
    } else {
        echo "✅ Uploads directory already exists<br>";
    }
    
    // 5. Test auth routes
    echo "<h2>5. Testing auth routes</h2>";
    $authRoutes = [
        '/auth/register',
        '/auth/register/step2',
        '/auth/verify-otp',
        '/dashboard'
    ];
    
    foreach ($authRoutes as $route) {
        echo "<a href='/jobspace$route' target='_blank'>Test: $route</a><br>";
    }
    
    echo "<h2>✅ Authentication System Setup Complete!</h2>";
    echo "<div style='background: #f0f9ff; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>🎉 Ready to Test!</h3>";
    echo "<p><strong>Registration Process:</strong></p>";
    echo "<ol>";
    echo "<li><a href='/jobspace/auth/register' target='_blank'>Start Registration (Step 1)</a></li>";
    echo "<li>Fill personal information</li>";
    echo "<li>Complete additional details (Step 2)</li>";
    echo "<li>Verify OTP (check error logs for OTP code)</li>";
    echo "<li>Access dashboard</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div style='background: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>📧 Email Configuration</h3>";
    echo "<p>Email credentials are configured in .env file:</p>";
    echo "<ul>";
    echo "<li>MAIL_HOST: " . ($_ENV['MAIL_HOST'] ?? 'Not set') . "</li>";
    echo "<li>MAIL_USERNAME: " . ($_ENV['MAIL_USERNAME'] ?? 'Not set') . "</li>";
    echo "<li>MAIL_PASSWORD: " . (!empty($_ENV['MAIL_PASSWORD']) ? 'Set' : 'Not set') . "</li>";
    echo "</ul>";
    echo "<p>If email is not configured, OTP codes will be logged to error log.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
    echo "Stack trace: <pre>" . $e->getTraceAsString() . "</pre>";
}
?>

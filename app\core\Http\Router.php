<?php

namespace App\Core\Http;

use App\Core\Container\Container;
use App\Core\Http\Request;
use App\Core\Http\Response;
use Exception;

/**
 * High-Performance Router
 * Optimized for 50K+ concurrent requests
 */
class Router
{
    private Container $container;
    private array $routes = [];
    private array $namedRoutes = [];
    private array $middlewares = [];
    private array $groupStack = [];
    private string $currentGroupPrefix = '';

    public function __construct(Container $container)
    {
        $this->container = $container;
    }

    /**
     * Register GET route
     */
    public function get(string $uri, $action): Route
    {
        return $this->addRoute(['GET'], $uri, $action);
    }

    /**
     * Register POST route
     */
    public function post(string $uri, $action): Route
    {
        return $this->addRoute(['POST'], $uri, $action);
    }

    /**
     * Register PUT route
     */
    public function put(string $uri, $action): Route
    {
        return $this->addRoute(['PUT'], $uri, $action);
    }

    /**
     * Register PATCH route
     */
    public function patch(string $uri, $action): Route
    {
        return $this->addRoute(['PATCH'], $uri, $action);
    }

    /**
     * Register DELETE route
     */
    public function delete(string $uri, $action): Route
    {
        return $this->addRoute(['DELETE'], $uri, $action);
    }

    /**
     * Register OPTIONS route
     */
    public function options(string $uri, $action): Route
    {
        return $this->addRoute(['OPTIONS'], $uri, $action);
    }

    /**
     * Register route for multiple methods
     */
    public function match(array $methods, string $uri, $action): Route
    {
        return $this->addRoute($methods, $uri, $action);
    }

    /**
     * Register route for all methods
     */
    public function any(string $uri, $action): Route
    {
        return $this->addRoute(['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'], $uri, $action);
    }

    /**
     * Add route to collection
     */
    private function addRoute(array $methods, string $uri, $action): Route
    {
        $uri = $this->currentGroupPrefix . '/' . trim($uri, '/');
        $uri = '/' . trim($uri, '/');
        
        $route = new Route($methods, $uri, $action);
        
        // Apply group middlewares
        if (!empty($this->groupStack)) {
            $groupMiddlewares = [];
            foreach ($this->groupStack as $group) {
                if (isset($group['middleware'])) {
                    $groupMiddlewares = array_merge($groupMiddlewares, (array) $group['middleware']);
                }
            }
            $route->middleware($groupMiddlewares);
        }

        foreach ($methods as $method) {
            $this->routes[$method][$uri] = $route;
        }

        return $route;
    }

    /**
     * Route group
     */
    public function group(array $attributes, callable $callback): void
    {
        $this->groupStack[] = $attributes;

        $oldPrefix = $this->currentGroupPrefix;

        if (isset($attributes['prefix'])) {
            $this->currentGroupPrefix .= '/' . trim($attributes['prefix'], '/');
        }

        $callback($this);

        array_pop($this->groupStack);

        // Reset prefix to previous state
        $this->currentGroupPrefix = $oldPrefix;
    }

    /**
     * Dispatch request
     */
    public function dispatch(Request $request): Response
    {
        $method = $request->getMethod();
        $uri = $request->getPathInfo();

        // Normalize URI
        $uri = '/' . trim($uri, '/');
        if ($uri !== '/' && substr($uri, -1) === '/') {
            $uri = substr($uri, 0, -1);
        }

        // Find matching route
        $route = $this->findRoute($method, $uri);

        if ($route === null) {
            return $this->handleNotFound();
        }

        // Set route parameters in request
        $request->setRouteParameters($route->getParameters());

        // Execute middlewares and action
        return $this->runRoute($request, $route);
    }

    /**
     * Find matching route
     */
    private function findRoute(string $method, string $uri): ?Route
    {
        // Check exact match first
        if (isset($this->routes[$method][$uri])) {
            return $this->routes[$method][$uri];
        }

        // Check pattern matches
        if (isset($this->routes[$method])) {
            foreach ($this->routes[$method] as $routeUri => $route) {
                if ($this->matchRoute($routeUri, $uri, $route)) {
                    return $route;
                }
            }
        }

        return null;
    }

    /**
     * Match route pattern
     */
    private function matchRoute(string $routeUri, string $requestUri, Route $route): bool
    {
        // Convert route pattern to regex
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $routeUri);
        $pattern = '#^' . $pattern . '$#';

        if (preg_match($pattern, $requestUri, $matches)) {
            array_shift($matches); // Remove full match
            
            // Extract parameter names
            preg_match_all('/\{([^}]+)\}/', $routeUri, $paramNames);
            $paramNames = $paramNames[1];

            // Set parameters
            $parameters = [];
            foreach ($paramNames as $index => $name) {
                if (isset($matches[$index])) {
                    $parameters[$name] = $matches[$index];
                }
            }
            
            $route->setParameters($parameters);
            return true;
        }

        return false;
    }

    /**
     * Run route with middlewares
     */
    private function runRoute(Request $request, Route $route): Response
    {
        $middlewares = $route->getMiddlewares();
        
        // Create middleware pipeline
        $pipeline = array_reduce(
            array_reverse($middlewares),
            function ($next, $middleware) {
                return function ($request) use ($middleware, $next) {
                    return $this->callMiddleware($middleware, $request, $next);
                };
            },
            function ($request) use ($route) {
                return $this->callAction($route->getAction(), $request, $route->getParameters());
            }
        );

        return $pipeline($request);
    }

    /**
     * Call middleware
     */
    private function callMiddleware($middleware, Request $request, callable $next): Response
    {
        if (is_string($middleware)) {
            $middleware = $this->container->get($middleware);
        }

        if (method_exists($middleware, 'handle')) {
            return $middleware->handle($request, $next);
        }

        return $next($request);
    }

    /**
     * Call route action
     */
    private function callAction($action, Request $request, array $parameters): Response
    {
        if (is_string($action)) {
            // Controller@method format
            if (strpos($action, '@') !== false) {
                [$controller, $method] = explode('@', $action);
                $controllerInstance = $this->container->get($controller);
                $result = $this->container->call([$controllerInstance, $method], $parameters);
            } else {
                // Single controller method
                $result = $this->container->call($action, $parameters);
            }
        } elseif (is_callable($action)) {
            $result = $this->container->call($action, $parameters);
        } else {
            throw new Exception("Invalid route action");
        }

        // Convert result to Response
        if ($result instanceof Response) {
            return $result;
        }

        if (is_array($result) || is_object($result)) {
            return new Response(json_encode($result), 200, ['Content-Type' => 'application/json']);
        }

        return new Response((string) $result);
    }

    /**
     * Handle 404 Not Found
     */
    private function handleNotFound(): Response
    {
        // Check if it's an API request
        $uri = $_SERVER['REQUEST_URI'] ?? '/';
        if (strpos($uri, '/api/') !== false) {
            return new Response(
                json_encode(['error' => 'Route not found']),
                404,
                ['Content-Type' => 'application/json']
            );
        }

        // For web requests, show a nice 404 page
        $html = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 - Page Not Found</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen flex items-center justify-center">
        <div class="bg-white p-8 rounded-lg shadow-md text-center">
            <h1 class="text-6xl font-bold text-red-500 mb-4">404</h1>
            <p class="text-gray-600 mb-4">Sorry, the page you are looking for could not be found.</p>
            <p class="text-sm text-gray-500 mb-4">URI: ' . htmlspecialchars($uri) . '</p>
            <a href="/jobspace/" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Go Home</a>
        </div>
    </div>
</body>
</html>';

        return new Response($html, 404);
    }

    /**
     * Generate URL for named route
     */
    public function url(string $name, array $parameters = []): string
    {
        if (!isset($this->namedRoutes[$name])) {
            throw new Exception("Route [{$name}] not found");
        }

        $route = $this->namedRoutes[$name];
        $uri = $route->getUri();

        foreach ($parameters as $key => $value) {
            $uri = str_replace('{' . $key . '}', $value, $uri);
        }

        return $uri;
    }

    /**
     * Get all routes
     */
    public function getRoutes(): array
    {
        return $this->routes;
    }
}

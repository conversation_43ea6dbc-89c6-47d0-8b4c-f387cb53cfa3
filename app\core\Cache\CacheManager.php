<?php

namespace App\Core\Cache;

use App\Core\Config\ConfigManager;
use Exception;

/**
 * High-Performance Cache Manager
 * File-based caching with auto-invalidation
 */
class CacheManager
{
    private ConfigManager $config;
    private string $cachePath;
    private string $prefix;
    private int $defaultTtl;
    private array $tags = [];

    public function __construct(ConfigManager $config)
    {
        $this->config = $config;
        $this->cachePath = $config->get('cache.stores.file.path', BASE_PATH . '/storage/cache');
        $this->prefix = $config->get('cache.prefix', 'jobspace_cache');
        $this->defaultTtl = $config->get('cache.ttl', 3600);
        
        $this->ensureCacheDirectory();
    }

    /**
     * Store item in cache
     */
    public function put(string $key, $value, int $ttl = null): bool
    {
        $ttl = $ttl ?? $this->defaultTtl;
        $expiry = time() + $ttl;
        
        $data = [
            'value' => $value,
            'expiry' => $expiry,
            'tags' => $this->tags,
            'created_at' => time()
        ];

        $filePath = $this->getFilePath($key);
        $this->ensureDirectory(dirname($filePath));

        $success = file_put_contents(
            $filePath,
            serialize($data),
            LOCK_EX
        ) !== false;

        $this->tags = []; // Reset tags
        return $success;
    }

    /**
     * Get item from cache
     */
    public function get(string $key, $default = null)
    {
        $filePath = $this->getFilePath($key);
        
        if (!file_exists($filePath)) {
            return $default;
        }

        $content = file_get_contents($filePath);
        if ($content === false) {
            return $default;
        }

        $data = unserialize($content);
        if (!is_array($data) || !isset($data['expiry'])) {
            $this->forget($key);
            return $default;
        }

        // Check if expired
        if (time() > $data['expiry']) {
            $this->forget($key);
            return $default;
        }

        return $data['value'];
    }

    /**
     * Get item or store default value
     */
    public function remember(string $key, callable $callback, int $ttl = null)
    {
        $value = $this->get($key);
        
        if ($value !== null) {
            return $value;
        }

        $value = $callback();
        $this->put($key, $value, $ttl);
        
        return $value;
    }

    /**
     * Store item forever (until manually deleted)
     */
    public function forever(string $key, $value): bool
    {
        return $this->put($key, $value, 315360000); // 10 years
    }

    /**
     * Remove item from cache
     */
    public function forget(string $key): bool
    {
        $filePath = $this->getFilePath($key);
        
        if (file_exists($filePath)) {
            return unlink($filePath);
        }
        
        return true;
    }

    /**
     * Check if item exists in cache
     */
    public function has(string $key): bool
    {
        return $this->get($key) !== null;
    }

    /**
     * Increment numeric value
     */
    public function increment(string $key, int $value = 1): int
    {
        $current = (int) $this->get($key, 0);
        $new = $current + $value;
        $this->put($key, $new);
        
        return $new;
    }

    /**
     * Decrement numeric value
     */
    public function decrement(string $key, int $value = 1): int
    {
        return $this->increment($key, -$value);
    }

    /**
     * Add tags to next cache operation
     */
    public function tags(array $tags): self
    {
        $this->tags = array_merge($this->tags, $tags);
        return $this;
    }

    /**
     * Flush cache by tags
     */
    public function flushTags(array $tags): int
    {
        $flushed = 0;
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($this->cachePath)
        );

        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'cache') {
                $content = file_get_contents($file->getPathname());
                $data = unserialize($content);
                
                if (is_array($data) && isset($data['tags'])) {
                    $intersection = array_intersect($tags, $data['tags']);
                    if (!empty($intersection)) {
                        unlink($file->getPathname());
                        $flushed++;
                    }
                }
            }
        }

        return $flushed;
    }

    /**
     * Clear all cache
     */
    public function flush(): bool
    {
        return $this->deleteDirectory($this->cachePath);
    }

    /**
     * Get cache statistics
     */
    public function stats(): array
    {
        $stats = [
            'total_files' => 0,
            'total_size' => 0,
            'expired_files' => 0,
            'valid_files' => 0,
            'oldest_file' => null,
            'newest_file' => null
        ];

        if (!is_dir($this->cachePath)) {
            return $stats;
        }

        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($this->cachePath)
        );

        $now = time();
        
        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'cache') {
                $stats['total_files']++;
                $stats['total_size'] += $file->getSize();
                
                $mtime = $file->getMTime();
                if ($stats['oldest_file'] === null || $mtime < $stats['oldest_file']) {
                    $stats['oldest_file'] = $mtime;
                }
                if ($stats['newest_file'] === null || $mtime > $stats['newest_file']) {
                    $stats['newest_file'] = $mtime;
                }

                // Check if expired
                $content = file_get_contents($file->getPathname());
                $data = unserialize($content);
                
                if (is_array($data) && isset($data['expiry'])) {
                    if ($now > $data['expiry']) {
                        $stats['expired_files']++;
                    } else {
                        $stats['valid_files']++;
                    }
                }
            }
        }

        $stats['total_size_mb'] = round($stats['total_size'] / 1024 / 1024, 2);
        
        return $stats;
    }

    /**
     * Clean expired cache files
     */
    public function cleanup(): int
    {
        $cleaned = 0;
        
        if (!is_dir($this->cachePath)) {
            return $cleaned;
        }

        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($this->cachePath)
        );

        $now = time();
        
        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'cache') {
                $content = file_get_contents($file->getPathname());
                $data = unserialize($content);
                
                if (is_array($data) && isset($data['expiry'])) {
                    if ($now > $data['expiry']) {
                        unlink($file->getPathname());
                        $cleaned++;
                    }
                }
            }
        }

        return $cleaned;
    }

    /**
     * Get cache file path
     */
    private function getFilePath(string $key): string
    {
        $hash = md5($this->prefix . $key);
        $dir = substr($hash, 0, 2);
        
        return $this->cachePath . '/' . $dir . '/' . $hash . '.cache';
    }

    /**
     * Ensure cache directory exists
     */
    private function ensureCacheDirectory(): void
    {
        if (!is_dir($this->cachePath)) {
            mkdir($this->cachePath, 0755, true);
        }
    }

    /**
     * Ensure directory exists
     */
    private function ensureDirectory(string $path): void
    {
        if (!is_dir($path)) {
            mkdir($path, 0755, true);
        }
    }

    /**
     * Delete directory recursively
     */
    private function deleteDirectory(string $path): bool
    {
        if (!is_dir($path)) {
            return true;
        }

        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($path, \RecursiveDirectoryIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::CHILD_FIRST
        );

        foreach ($iterator as $file) {
            if ($file->isDir()) {
                rmdir($file->getPathname());
            } else {
                unlink($file->getPathname());
            }
        }

        return rmdir($path);
    }

    /**
     * Generate cache key with prefix
     */
    public function key(string $key): string
    {
        return $this->prefix . ':' . $key;
    }

    /**
     * Store multiple items
     */
    public function putMany(array $items, int $ttl = null): bool
    {
        $success = true;
        
        foreach ($items as $key => $value) {
            if (!$this->put($key, $value, $ttl)) {
                $success = false;
            }
        }
        
        return $success;
    }

    /**
     * Get multiple items
     */
    public function getMany(array $keys): array
    {
        $results = [];
        
        foreach ($keys as $key) {
            $results[$key] = $this->get($key);
        }
        
        return $results;
    }

    /**
     * Get cache size in bytes
     */
    public function size(): int
    {
        $size = 0;
        
        if (!is_dir($this->cachePath)) {
            return $size;
        }

        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($this->cachePath)
        );

        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $size += $file->getSize();
            }
        }

        return $size;
    }
}

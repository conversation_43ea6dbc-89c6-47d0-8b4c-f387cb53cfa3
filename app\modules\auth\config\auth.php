<?php

return [
    // Authentication settings
    'session' => [
        'lifetime' => 1800, // 30 minutes
        'name' => 'jobspace_session',
        'secure' => false, // Set to true in production with HTTPS
        'httponly' => true,
        'samesite' => 'Lax'
    ],
    
    // Password settings
    'password' => [
        'min_length' => 8,
        'require_uppercase' => true,
        'require_lowercase' => true,
        'require_numbers' => true,
        'require_symbols' => false,
        'reset_token_expiry' => 3600 // 1 hour
    ],
    
    // OTP settings
    'otp' => [
        'length' => 6,
        'expiry' => 600, // 10 minutes
        'max_attempts' => 3,
        'resend_cooldown' => 60 // 1 minute
    ],
    
    // Registration settings
    'registration' => [
        'multi_step' => true,
        'email_unique' => true,
        'username_unique' => true,
        'phone_unique' => true,
        'auto_login_after_verification' => true,
        'welcome_email' => true,
        'default_role' => 'user',
        'allowed_roles' => ['user', 'business', 'creator', 'admin']
    ],
    
    // File upload settings
    'uploads' => [
        'profile_picture' => [
            'max_size' => 2048, // 2MB in KB
            'allowed_types' => ['jpg', 'jpeg', 'png', 'gif'],
            'path' => 'uploads/profiles/',
            'default' => 'assets/images/default-avatar.png'
        ]
    ],
    
    // Email templates
    'email_templates' => [
        'verification' => 'auth/emails/verification',
        'otp' => 'auth/emails/otp',
        'welcome' => 'auth/emails/welcome',
        'password_reset' => 'auth/emails/password_reset'
    ],
    
    // Security settings
    'security' => [
        'max_login_attempts' => 5,
        'lockout_duration' => 900, // 15 minutes
        'csrf_protection' => true,
        'rate_limiting' => true
    ]
];

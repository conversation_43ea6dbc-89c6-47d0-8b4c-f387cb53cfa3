# Change Log

All notable changes to `sebastian<PERSON>mann/code-unit-reverse-lookup` are documented in this file using the [Keep a CHANGELOG](http://keepachangelog.com/) principles.

## [3.0.0] - 2023-02-03

### Removed

* This component is no longer supported on PHP 7.3, PHP 7.4, and PHP 8.0

## [2.0.3] - 2020-09-28

### Changed

* Changed PHP version constraint in `composer.json` from `^7.3 || ^8.0` to `>=7.3`

## [2.0.2] - 2020-06-26

### Added

* This component is now supported on PHP 8

## [2.0.1] - 2020-06-15

### Changed

* Tests etc. are now ignored for archive exports

## 2.0.0 - 2020-02-07

### Removed

* This component is no longer supported on PHP 5.6, PHP 7.0, PHP 7.1, and PHP 7.2

## 1.0.0 - 2016-02-13

### Added

* Initial release

[3.0.0]: https://github.com/sebastianbergmann/code-unit-reverse-lookup/compare/2.0.3...3.0.0
[2.0.3]: https://github.com/sebastianbergmann/code-unit-reverse-lookup/compare/2.0.2...2.0.3
[2.0.2]: https://github.com/sebastianbergmann/code-unit-reverse-lookup/compare/2.0.1...2.0.2
[2.0.1]: https://github.com/sebastianbergmann/code-unit-reverse-lookup/compare/2.0.0...2.0.1
[2.0.0]: https://github.com/sebastianbergmann/code-unit-reverse-lookup/compare/1.0.0...2.0.0

<?php

namespace App\Modules\Auth\Controllers;

use App\Modules\Auth\Services\AuthService;
use App\Modules\Auth\Services\EmailService;
use App\Modules\Auth\Models\User;

class AuthController
{
    private AuthService $authService;
    private EmailService $emailService;
    
    public function __construct()
    {
        $this->authService = new AuthService();
        $this->emailService = new EmailService();
    }
    
    /**
     * Show registration step 1 form
     */
    public function register(): string
    {
        $data = [
            'errors' => $_SESSION['registration_errors'] ?? [],
            'old_data' => $_SESSION['registration_data'] ?? []
        ];
        
        // Clear session data
        unset($_SESSION['registration_errors'], $_SESSION['registration_data']);
        
        return $this->view('pages/register', $data);
    }
    
    /**
     * Show registration step 2 form
     */
    public function registerStep2(): string
    {
        // Check if step 1 is completed
        if (!isset($_SESSION['registration_step1'])) {
            header('Location: /jobspace/auth/register');
            exit;
        }
        
        $data = [
            'errors' => $_SESSION['registration_errors'] ?? [],
            'old_data' => $_SESSION['registration_data'] ?? [],
            'step1_data' => $_SESSION['registration_step1'],
            'admin_exists' => User::adminExists()
        ];
        
        // Clear session data
        unset($_SESSION['registration_errors'], $_SESSION['registration_data']);
        
        return $this->view('pages/register-step2', $data);
    }
    
    /**
     * Process registration step 1
     */
    public function processStep1(): string
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /jobspace/auth/register');
            exit;
        }

        $data = $_POST;
        $errors = $this->authService->validateStep1($data);

        if (!empty($errors)) {
            $_SESSION['registration_errors'] = $errors;
            $_SESSION['registration_data'] = $data;
            header('Location: /jobspace/auth/register');
            exit;
        }

        // Store step 1 data in session
        $_SESSION['registration_step1'] = $data;
        unset($_SESSION['registration_errors'], $_SESSION['registration_data']);

        header('Location: /jobspace/auth/register/step2');
        exit;
    }
    
    /**
     * Process registration step 2 and send verification
     */
    public function processStep2(): string
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /jobspace/auth/register/step2');
            exit;
        }

        if (!isset($_SESSION['registration_step1'])) {
            header('Location: /jobspace/auth/register');
            exit;
        }

        $step1Data = $_SESSION['registration_step1'];
        $step2Data = $_POST;

        $errors = $this->authService->validateStep2($step2Data);

        if (!empty($errors)) {
            $_SESSION['registration_errors'] = $errors;
            $_SESSION['registration_data'] = $step2Data;
            header('Location: /jobspace/auth/register/step2');
            exit;
        }

        // Process registration
        $result = $this->authService->processRegistration($step1Data, $step2Data);

        if ($result['success']) {
            // Send verification email
            $name = $step1Data['first_name'] . ' ' . $step1Data['last_name'];
            try {
                $this->emailService->sendVerificationEmail(
                    $result['email'],
                    $name,
                    $result['verification_token'],
                    $result['otp']
                );
            } catch (\Exception $e) {
                error_log("Verification email failed: " . $e->getMessage());
                // Continue even if email fails
            }

            // Clean up session
            unset($_SESSION['registration_step1'], $_SESSION['registration_errors'], $_SESSION['registration_data']);

            // Redirect to verification page
            $_SESSION['verification_email'] = $result['email'];
            header('Location: /jobspace/auth/verify-otp');
            exit;
        }

        $_SESSION['registration_errors'] = ['general' => 'Registration failed. Please try again.'];
        header('Location: /jobspace/auth/register/step2');
        exit;
    }
    
    /**
     * Show OTP verification page
     */
    public function verifyOTP(): string
    {
        if (!isset($_SESSION['verification_email'])) {
            header('Location: /jobspace/auth/register');
            exit;
        }

        $data = [
            'email' => $_SESSION['verification_email'],
            'error' => $_SESSION['verification_error'] ?? null
        ];
        
        unset($_SESSION['verification_error']);

        return $this->view('pages/verify-otp', $data);
    }
    
    /**
     * Process OTP verification
     */
    public function processOTPVerification(): string
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /jobspace/auth/verify-otp');
            exit;
        }

        if (!isset($_SESSION['verification_email'])) {
            header('Location: /jobspace/auth/register');
            exit;
        }

        $email = $_SESSION['verification_email'];
        $otp = $_POST['otp'] ?? '';

        $result = $this->authService->verifyOTP($email, $otp);

        if ($result['success']) {
            // Get user data from result
            $userData = $result['user_data'];
            $name = $userData['first_name'] . ' ' . $userData['last_name'];
            
            // Send welcome email
            try {
                $this->emailService->sendWelcomeEmail($email, $name, $userData);
            } catch (\Exception $e) {
                error_log("Welcome email failed: " . $e->getMessage());
                // Continue even if email fails
            }

            // Clean up session
            unset($_SESSION['verification_email']);

            // Auto login if configured
            $_SESSION['user'] = $userData;
            $_SESSION['success_message'] = 'Account created successfully! Welcome to JobSpace.';

            header('Location: /jobspace/dashboard');
            exit;
        }

        $_SESSION['verification_error'] = $result['message'];
        header('Location: /jobspace/auth/verify-otp');
        exit;
    }
    
    /**
     * Resend OTP
     */
    public function resendOTP(): string
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            exit;
        }

        if (!isset($_SESSION['verification_email'])) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'No verification session found']);
            exit;
        }

        $email = $_SESSION['verification_email'];
        $verificationData = User::getVerificationData($email);

        if (!$verificationData) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'No verification data found']);
            exit;
        }

        // Generate new OTP
        $newOTP = User::generateOTP();
        $verificationData['otp'] = $newOTP;
        $verificationData['otp_expires'] = time() + 600; // 10 minutes

        User::storeVerificationData($email, $verificationData);

        // Send new OTP
        $userData = $verificationData['user_data'];
        $name = $userData['first_name'] . ' ' . $userData['last_name'];
        $this->emailService->sendOTPEmail($email, $name, $newOTP);

        header('Content-Type: application/json');
        echo json_encode(['success' => true, 'message' => 'New OTP sent successfully']);
        exit;
    }
    
    /**
     * Check email availability (AJAX)
     */
    public function checkEmailAvailability(): string
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['available' => false]);
            exit;
        }

        $email = $_POST['email'] ?? '';
        $user = User::findByEmail($email);

        header('Content-Type: application/json');
        echo json_encode(['available' => $user === null]);
        exit;
    }
    
    /**
     * Check username availability (AJAX)
     */
    public function checkUsernameAvailability(): string
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['available' => false]);
            exit;
        }

        $username = $_POST['username'] ?? '';
        $user = User::findByUsername($username);

        header('Content-Type: application/json');
        echo json_encode(['available' => $user === null]);
        exit;
    }
    
    /**
     * Dashboard (temporary)
     */
    public function dashboard(): string
    {
        if (!isset($_SESSION['user'])) {
            header('Location: /jobspace/auth/login');
            exit;
        }
        
        return $this->view('pages/dashboard', ['user' => $_SESSION['user']]);
    }
    
    /**
     * Render view
     */
    private function view(string $view, array $data = []): string
    {
        extract($data);
        ob_start();
        include BASE_PATH . "/app/modules/auth/views/$view.php";
        return ob_get_clean();
    }
}

<?php

namespace App\Core;

class SessionManager
{
    private static bool $started = false;
    private static array $config = [];

    /**
     * Initialize session configuration
     */
    public static function init(): void
    {
        if (self::$started) {
            return;
        }

        self::$config = [
            'lifetime' => $_ENV['SESSION_LIFETIME'] ?? 120,
            'path' => $_ENV['SESSION_PATH'] ?? '/',
            'domain' => $_ENV['SESSION_DOMAIN'] ?? '',
            'secure' => $_ENV['SESSION_SECURE_COOKIE'] ?? false,
            'httponly' => $_ENV['SESSION_HTTP_ONLY'] ?? true,
            'samesite' => $_ENV['SESSION_SAME_SITE'] ?? 'Lax'
        ];

        // Configure session settings
        ini_set('session.cookie_lifetime', self::$config['lifetime'] * 60);
        ini_set('session.cookie_path', self::$config['path']);
        ini_set('session.cookie_domain', self::$config['domain']);
        ini_set('session.cookie_secure', self::$config['secure']);
        ini_set('session.cookie_httponly', self::$config['httponly']);
        ini_set('session.cookie_samesite', self::$config['samesite']);
        ini_set('session.use_strict_mode', 1);
        ini_set('session.gc_maxlifetime', self::$config['lifetime'] * 60);

        // Use database session handler if configured
        if (($_ENV['SESSION_DRIVER'] ?? 'file') === 'database') {
            session_set_save_handler(
                [self::class, 'sessionOpen'],
                [self::class, 'sessionClose'],
                [self::class, 'sessionRead'],
                [self::class, 'sessionWrite'],
                [self::class, 'sessionDestroy'],
                [self::class, 'sessionGc']
            );
        }

        session_start();
        self::$started = true;

        // Regenerate session ID periodically for security
        if (!isset($_SESSION['last_regeneration'])) {
            self::regenerateId();
        } elseif (time() - $_SESSION['last_regeneration'] > 300) { // 5 minutes
            self::regenerateId();
        }
    }

    /**
     * Regenerate session ID
     */
    public static function regenerateId(): void
    {
        session_regenerate_id(true);
        $_SESSION['last_regeneration'] = time();
    }

    /**
     * Set session value
     */
    public static function set(string $key, mixed $value): void
    {
        self::init();
        $_SESSION[$key] = $value;
    }

    /**
     * Get session value
     */
    public static function get(string $key, mixed $default = null): mixed
    {
        self::init();
        return $_SESSION[$key] ?? $default;
    }

    /**
     * Check if session key exists
     */
    public static function has(string $key): bool
    {
        self::init();
        return isset($_SESSION[$key]);
    }

    /**
     * Remove session key
     */
    public static function remove(string $key): void
    {
        self::init();
        unset($_SESSION[$key]);
    }

    /**
     * Clear all session data
     */
    public static function clear(): void
    {
        self::init();
        $_SESSION = [];
    }

    /**
     * Destroy session
     */
    public static function destroy(): void
    {
        self::init();
        $_SESSION = [];
        
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }
        
        session_destroy();
        self::$started = false;
    }

    /**
     * Flash message - set message that will be available only for next request
     */
    public static function flash(string $key, mixed $value): void
    {
        self::set("flash_{$key}", $value);
    }

    /**
     * Get flash message and remove it
     */
    public static function getFlash(string $key, mixed $default = null): mixed
    {
        $value = self::get("flash_{$key}", $default);
        self::remove("flash_{$key}");
        return $value;
    }

    /**
     * Check if flash message exists
     */
    public static function hasFlash(string $key): bool
    {
        return self::has("flash_{$key}");
    }

    /**
     * Login user
     */
    public static function login(array $user): void
    {
        self::regenerateId();
        self::set('user_id', $user['id']);
        self::set('user', $user);
        self::set('logged_in', true);
        self::set('login_time', time());
        
        // Store session in database if using database driver
        if (($_ENV['SESSION_DRIVER'] ?? 'file') === 'database') {
            self::storeSessionInDatabase($user['id']);
        }
    }

    /**
     * Logout user
     */
    public static function logout(): void
    {
        $userId = self::get('user_id');
        
        // Remove session from database if using database driver
        if (($_ENV['SESSION_DRIVER'] ?? 'file') === 'database' && $userId) {
            self::removeSessionFromDatabase();
        }
        
        self::destroy();
    }

    /**
     * Check if user is logged in
     */
    public static function isLoggedIn(): bool
    {
        return self::get('logged_in', false) && self::get('user_id') !== null;
    }

    /**
     * Get current user
     */
    public static function getUser(): ?array
    {
        return self::get('user');
    }

    /**
     * Get current user ID
     */
    public static function getUserId(): ?int
    {
        return self::get('user_id');
    }

    /**
     * Database session handlers
     */
    public static function sessionOpen($savePath, $sessionName): bool
    {
        return true;
    }

    public static function sessionClose(): bool
    {
        return true;
    }

    public static function sessionRead($sessionId): string
    {
        try {
            $result = Database::fetch(
                "SELECT payload FROM sessions WHERE id = ?",
                [$sessionId]
            );
            return $result['payload'] ?? '';
        } catch (\Exception $e) {
            return '';
        }
    }

    public static function sessionWrite($sessionId, $data): bool
    {
        try {
            $userId = $_SESSION['user_id'] ?? null;
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '';
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            
            // Check if session exists
            $existing = Database::fetch(
                "SELECT id FROM sessions WHERE id = ?",
                [$sessionId]
            );
            
            if ($existing) {
                Database::update(
                    'sessions',
                    [
                        'user_id' => $userId,
                        'ip_address' => $ipAddress,
                        'user_agent' => $userAgent,
                        'payload' => $data,
                        'last_activity' => date('Y-m-d H:i:s')
                    ],
                    'id = :id',
                    ['id' => $sessionId]
                );
            } else {
                Database::insert('sessions', [
                    'id' => $sessionId,
                    'user_id' => $userId,
                    'ip_address' => $ipAddress,
                    'user_agent' => $userAgent,
                    'payload' => $data,
                    'last_activity' => date('Y-m-d H:i:s')
                ]);
            }
            
            return true;
        } catch (\Exception $e) {
            error_log("Session write failed: " . $e->getMessage());
            return false;
        }
    }

    public static function sessionDestroy($sessionId): bool
    {
        try {
            Database::delete('sessions', 'id = ?', [$sessionId]);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    public static function sessionGc($maxLifetime): int
    {
        try {
            $expiredTime = date('Y-m-d H:i:s', time() - $maxLifetime);
            return Database::delete('sessions', 'last_activity < ?', [$expiredTime]);
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Store session in database
     */
    private static function storeSessionInDatabase(int $userId): void
    {
        try {
            $sessionId = session_id();
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '';
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            
            Database::insert('sessions', [
                'id' => $sessionId,
                'user_id' => $userId,
                'ip_address' => $ipAddress,
                'user_agent' => $userAgent,
                'payload' => session_encode(),
                'last_activity' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            error_log("Session storage failed: " . $e->getMessage());
        }
    }

    /**
     * Remove session from database
     */
    private static function removeSessionFromDatabase(): void
    {
        try {
            $sessionId = session_id();
            Database::delete('sessions', 'id = ?', [$sessionId]);
        } catch (\Exception $e) {
            error_log("Session removal failed: " . $e->getMessage());
        }
    }
}

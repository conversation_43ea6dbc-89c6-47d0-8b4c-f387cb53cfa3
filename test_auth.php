<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Authentication System Test</h1>";

// Test database connection
echo "<h2>Database Connection Test</h2>";
try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=jobspace', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Database connection successful!<br>";
    
    // Check tables
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "Tables: " . implode(', ', $tables) . "<br>";
    
    // Check users table structure
    if (in_array('users', $tables)) {
        echo "<h3>Users Table Structure:</h3>";
        $stmt = $pdo->query("DESCRIBE users");
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "- {$row['Field']}: {$row['Type']}<br>";
        }
    }
    
    // Check verification_tokens table
    if (in_array('verification_tokens', $tables)) {
        echo "<h3>Verification Tokens Table Structure:</h3>";
        $stmt = $pdo->query("DESCRIBE verification_tokens");
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "- {$row['Field']}: {$row['Type']}<br>";
        }
    } else {
        echo "<h3>Creating verification_tokens table...</h3>";
        $sql = "
        CREATE TABLE verification_tokens (
            id INT AUTO_INCREMENT PRIMARY KEY,
            email VARCHAR(255) NOT NULL,
            token VARCHAR(255) NOT NULL,
            otp VARCHAR(6) NOT NULL,
            user_data LONGTEXT NOT NULL,
            expires_at TIMESTAMP NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_email (email)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        ";
        $pdo->exec($sql);
        echo "✅ verification_tokens table created!<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

// Test email configuration
echo "<h2>Email Configuration Test</h2>";
$emailConfig = [
    'host' => $_ENV['MAIL_HOST'] ?? 'smtp.gmail.com',
    'port' => $_ENV['MAIL_PORT'] ?? 587,
    'username' => $_ENV['MAIL_USERNAME'] ?? '',
    'password' => $_ENV['MAIL_PASSWORD'] ?? '',
    'encryption' => $_ENV['MAIL_ENCRYPTION'] ?? 'tls',
    'from_address' => $_ENV['MAIL_FROM_ADDRESS'] ?? '<EMAIL>',
    'from_name' => $_ENV['MAIL_FROM_NAME'] ?? 'JobSpace'
];

foreach ($emailConfig as $key => $value) {
    $displayValue = ($key === 'password') ? (empty($value) ? 'NOT SET' : 'SET') : $value;
    echo "$key: $displayValue<br>";
}

echo "<h2>Test Registration Process</h2>";
echo "<a href='/jobspace/auth/register'>Go to Registration</a><br>";
echo "<a href='/jobspace/debug.php'>Go to Debug Page</a><br>";
?>

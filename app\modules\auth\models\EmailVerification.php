<?php

namespace App\Modules\Auth\Models;

use App\Core\Model;
use App\Core\Security\Token;

class EmailVerification extends Model
{
    protected $table = 'email_verification_tokens';
    protected $primaryKey = 'id';
    
    protected $fillable = [
        'user_id', 'token', 'otp', 'expires_at'
    ];

    /**
     * Create verification token and OTP
     */
    public function createVerification(int $userId): array
    {
        // Delete existing tokens for this user
        $this->deleteByUserId($userId);
        
        $token = Token::generate(64);
        $otp = $this->generateOTP();
        $expiresAt = date('Y-m-d H:i:s', time() + 600); // 10 minutes
        
        $data = [
            'user_id' => $userId,
            'token' => $token,
            'otp' => $otp,
            'expires_at' => $expiresAt
        ];
        
        $id = $this->create($data);
        
        return [
            'id' => $id,
            'token' => $token,
            'otp' => $otp,
            'expires_at' => $expiresAt
        ];
    }

    /**
     * Find verification by token
     */
    public function findByToken(string $token): ?array
    {
        $sql = "SELECT * FROM {$this->table} WHERE token = ? AND expires_at > NOW() AND verified_at IS NULL LIMIT 1";
        $result = $this->db->query($sql, [$token]);
        return $result ? $result[0] : null;
    }

    /**
     * Find verification by OTP
     */
    public function findByOTP(string $otp, int $userId): ?array
    {
        $sql = "SELECT * FROM {$this->table} WHERE otp = ? AND user_id = ? AND expires_at > NOW() AND verified_at IS NULL LIMIT 1";
        $result = $this->db->query($sql, [$otp, $userId]);
        return $result ? $result[0] : null;
    }

    /**
     * Mark as verified
     */
    public function markAsVerified(int $id): bool
    {
        $sql = "UPDATE {$this->table} SET verified_at = NOW() WHERE id = ?";
        return $this->db->execute($sql, [$id]);
    }

    /**
     * Delete verification tokens by user ID
     */
    public function deleteByUserId(int $userId): bool
    {
        $sql = "DELETE FROM {$this->table} WHERE user_id = ?";
        return $this->db->execute($sql, [$userId]);
    }

    /**
     * Clean expired tokens
     */
    public function cleanExpiredTokens(): bool
    {
        $sql = "DELETE FROM {$this->table} WHERE expires_at < NOW()";
        return $this->db->execute($sql);
    }

    /**
     * Generate 6-digit OTP
     */
    private function generateOTP(): string
    {
        return str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
    }

    /**
     * Check if user has pending verification
     */
    public function hasPendingVerification(int $userId): bool
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE user_id = ? AND expires_at > NOW() AND verified_at IS NULL";
        $result = $this->db->query($sql, [$userId]);
        return $result[0]['count'] > 0;
    }

    /**
     * Get verification attempts count
     */
    public function getVerificationAttempts(int $userId, int $minutes = 60): int
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} 
                WHERE user_id = ? AND created_at > DATE_SUB(NOW(), INTERVAL ? MINUTE)";
        $result = $this->db->query($sql, [$userId, $minutes]);
        return $result[0]['count'] ?? 0;
    }
}

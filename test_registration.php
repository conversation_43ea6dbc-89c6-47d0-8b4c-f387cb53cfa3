<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load environment variables
if (file_exists('.env')) {
    $lines = file('.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"');
        }
    }
}

// Include necessary files
require_once 'app/core/Database.php';
require_once 'app/modules/auth/models/User.php';
require_once 'app/modules/auth/services/AuthService.php';
require_once 'app/modules/auth/services/EmailService.php';

use App\Modules\Auth\Models\User;
use App\Modules\Auth\Services\AuthService;
use App\Modules\Auth\Services\EmailService;

echo "<h1>Registration Process Test</h1>";

// Test 1: Database Connection
echo "<h2>1. Database Connection Test</h2>";
try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=jobspace', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Database connection successful!<br>";
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "<br>";
    exit;
}

// Test 2: Create test user data
echo "<h2>2. Test User Registration</h2>";
$testEmail = 'test' . time() . '@example.com';
$step1Data = [
    'first_name' => 'Test',
    'last_name' => 'User',
    'email' => $testEmail,
    'phone' => '01700000000',
    'date_of_birth' => '1990-01-01',
    'gender' => 'Male',
    'password' => 'password123',
    'confirm_password' => 'password123'
];

$step2Data = [
    'username' => 'testuser' . time(),
    'role' => 'user',
    'country' => 'Bangladesh',
    'city' => 'Dhaka',
    'bio' => 'Test user bio'
];

try {
    $authService = new AuthService();
    
    // Test step 1 validation
    echo "<h3>Step 1 Validation:</h3>";
    $step1Errors = $authService->validateStep1($step1Data);
    if (empty($step1Errors)) {
        echo "✅ Step 1 validation passed<br>";
    } else {
        echo "❌ Step 1 validation failed:<br>";
        foreach ($step1Errors as $field => $error) {
            echo "- $field: $error<br>";
        }
    }
    
    // Test step 2 validation
    echo "<h3>Step 2 Validation:</h3>";
    $step2Errors = $authService->validateStep2($step2Data);
    if (empty($step2Errors)) {
        echo "✅ Step 2 validation passed<br>";
    } else {
        echo "❌ Step 2 validation failed:<br>";
        foreach ($step2Errors as $field => $error) {
            echo "- $field: $error<br>";
        }
    }
    
    // Test registration process
    if (empty($step1Errors) && empty($step2Errors)) {
        echo "<h3>Registration Process:</h3>";
        $result = $authService->processRegistration($step1Data, $step2Data);
        
        if ($result['success']) {
            echo "✅ Registration process successful<br>";
            echo "Email: " . $result['email'] . "<br>";
            echo "Verification Token: " . $result['verification_token'] . "<br>";
            echo "OTP: " . $result['otp'] . "<br>";
            
            // Test email sending
            echo "<h3>Email Sending Test:</h3>";
            try {
                $emailService = new EmailService();
                $emailResult = $emailService->sendVerificationEmail(
                    $result['email'],
                    $step1Data['first_name'] . ' ' . $step1Data['last_name'],
                    $result['verification_token'],
                    $result['otp']
                );
                
                if ($emailResult) {
                    echo "✅ Verification email sent successfully<br>";
                } else {
                    echo "❌ Verification email failed<br>";
                }
            } catch (Exception $e) {
                echo "❌ Email service error: " . $e->getMessage() . "<br>";
            }
            
            // Test OTP verification
            echo "<h3>OTP Verification Test:</h3>";
            $otpResult = $authService->verifyOTP($result['email'], $result['otp']);
            
            if ($otpResult['success']) {
                echo "✅ OTP verification successful<br>";
                echo "User created in database: " . ($otpResult['user_data'] ? 'YES' : 'NO') . "<br>";
                
                // Check if user exists in database
                $createdUser = User::findByEmail($result['email']);
                if ($createdUser) {
                    echo "✅ User found in database<br>";
                    echo "User ID: " . $createdUser['id'] . "<br>";
                    echo "Username: " . $createdUser['username'] . "<br>";
                    echo "Status: " . $createdUser['status'] . "<br>";
                } else {
                    echo "❌ User not found in database<br>";
                }
            } else {
                echo "❌ OTP verification failed: " . $otpResult['message'] . "<br>";
            }
            
        } else {
            echo "❌ Registration process failed<br>";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Test failed with exception: " . $e->getMessage() . "<br>";
    echo "Stack trace: <pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>Test Complete</h2>";
echo "<a href='/jobspace/auth/register'>Go to Registration Page</a>";
?>

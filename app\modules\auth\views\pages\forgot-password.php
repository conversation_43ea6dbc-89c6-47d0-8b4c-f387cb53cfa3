<?php 
$title = "Forgot Password | JobSpace";
$bodyClass = "auth-container";
include BASE_PATH . '/resources/views/components/header/auth-header.php'; 
?>
    <div class="min-h-screen flex items-center justify-center p-4">
        <div class="bg-white rounded-2xl shadow-2xl w-full max-w-md">
            <!-- Header -->
            <div class="p-8 pb-6">
                <div class="text-center mb-8">
                    <div class="text-4xl mb-4">🔐</div>
                    <h1 class="text-2xl font-bold text-gray-800 mb-2">Forgot Password?</h1>
                    <p class="text-gray-600">No worries! Enter your email and we'll send you reset instructions.</p>
                </div>

                <!-- Success/Error Messages -->
                <?php if (isset($_SESSION['forgot_success'])): ?>
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                        <div class="flex items-center">
                            <span class="text-green-500 text-xl mr-2">✅</span>
                            <p class="text-green-700"><?= htmlspecialchars($_SESSION['forgot_success']) ?></p>
                        </div>
                    </div>
                    <?php unset($_SESSION['forgot_success']); ?>
                <?php endif; ?>

                <?php if (isset($_SESSION['forgot_error'])): ?>
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                        <div class="flex items-center">
                            <span class="text-red-500 text-xl mr-2">❌</span>
                            <p class="text-red-700"><?= htmlspecialchars($_SESSION['forgot_error']) ?></p>
                        </div>
                    </div>
                    <?php unset($_SESSION['forgot_error']); ?>
                <?php endif; ?>
            </div>

            <!-- Forgot Password Form -->
            <form action="/jobspace/auth/process-forgot-password" method="POST" class="px-8 pb-8" id="forgotForm">
                <!-- Email -->
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-semibold mb-2">Email Address</label>
                    <input type="email" name="email" id="email" required
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-purple-500 transition"
                           placeholder="Enter your registered email address">
                    <p class="text-xs text-gray-500 mt-2">
                        We'll send password reset instructions to this email address.
                    </p>
                </div>

                <!-- Submit Button -->
                <button type="submit" 
                        class="w-full bg-purple-500 text-white py-3 rounded-lg font-semibold hover:bg-purple-600 transition duration-200">
                    Send Reset Instructions
                </button>

                <!-- Back to Login -->
                <div class="text-center mt-6">
                    <a href="/jobspace/auth/login" 
                       class="text-gray-600 hover:text-gray-800 font-semibold flex items-center justify-center">
                        ← Back to Login
                    </a>
                </div>
            </form>

            <!-- Help Section -->
            <div class="px-8 pb-8">
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h3 class="font-semibold text-blue-800 mb-2">Need Help?</h3>
                    <div class="text-sm text-blue-700 space-y-1">
                        <p>• Check your spam/junk folder for the reset email</p>
                        <p>• Make sure you're using the email you registered with</p>
                        <p>• Contact support if you still can't access your account</p>
                    </div>
                    <div class="mt-3">
                        <a href="/jobspace/contact" 
                           class="text-blue-600 hover:text-blue-800 font-semibold text-sm">
                            📞 Contact Support
                        </a>
                    </div>
                </div>
            </div>

            <!-- Register Link -->
            <div class="px-8 pb-6 text-center">
                <p class="text-gray-600 text-sm">Don't have an account? 
                    <a href="/jobspace/auth/register" class="text-purple-500 hover:text-purple-600 font-semibold">Create one here</a>
                </p>
            </div>
        </div>
    </div>

    <script>
        // Form submission
        document.getElementById('forgotForm').addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            const email = document.getElementById('email').value;
            
            if (!email || !email.includes('@')) {
                e.preventDefault();
                alert('Please enter a valid email address');
                return;
            }
            
            submitBtn.disabled = true;
            submitBtn.textContent = 'Sending Instructions...';
            submitBtn.className = 'w-full bg-purple-400 text-white py-3 rounded-lg font-semibold cursor-not-allowed';
        });

        // Auto-focus email field
        document.getElementById('email').focus();
    </script>

<?php
include BASE_PATH . '/resources/views/components/footer/auth-footer.php';
?>

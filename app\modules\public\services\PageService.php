<?php

namespace App\Modules\Public\Services;

use App\Modules\Public\Models\PageModel;

class PageService
{
    /**
     * Render page with layout
     */
    public function renderPage(string $page, array $data = []): string
    {
        // Get page metadata
        $meta = PageModel::getPageMeta($page);
        
        // Merge with provided data
        $data = array_merge($data, $meta);
        
        // Get navigation and set active page
        $navigation = PageModel::getNavigation();
        foreach ($navigation as &$nav) {
            $nav['active'] = $this->isCurrentPage($nav['url']);
        }
        $data['navigation'] = $navigation;
        
        // Get footer links
        $data['footer_links'] = PageModel::getFooterLinks();
        
        // Render page
        return $this->renderView("pages/{$page}", $data);
    }

    /**
     * Render view with data
     */
    private function renderView(string $viewPath, array $data = []): string
    {
        $modulePath = BASE_PATH . '/app/modules/public';
        $viewFile = $modulePath . '/views/' . $viewPath . '.php';
        
        if (!file_exists($viewFile)) {
            return "View not found: {$viewPath}";
        }
        
        // Extract data to variables
        extract($data);
        
        // Start output buffering
        ob_start();
        
        // Include view file
        include $viewFile;
        
        // Return rendered content
        return ob_get_clean();
    }

    /**
     * Check if current page
     */
    private function isCurrentPage(string $url): bool
    {
        $currentUri = $_SERVER['REQUEST_URI'] ?? '/';
        $currentUri = parse_url($currentUri, PHP_URL_PATH);
        
        return $currentUri === parse_url($url, PHP_URL_PATH);
    }

    /**
     * Get platform statistics
     */
    public function getPlatformStats(): array
    {
        return PageModel::getPlatformStats();
    }

    /**
     * Get platform features
     */
    public function getPlatformFeatures(): array
    {
        return PageModel::getPlatformFeatures();
    }

    /**
     * Process contact form (placeholder)
     */
    public function processContactForm(array $data): array
    {
        // Validate data
        $errors = [];
        
        if (empty($data['name'])) {
            $errors[] = 'Name is required';
        }
        
        if (empty($data['email']) || !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Valid email is required';
        }
        
        if (empty($data['message'])) {
            $errors[] = 'Message is required';
        }
        
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }
        
        // Process form (send email, save to database, etc.)
        // For now, just return success
        
        return ['success' => true, 'message' => 'Thank you for your message!'];
    }
}

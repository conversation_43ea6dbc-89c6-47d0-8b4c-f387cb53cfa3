<?php

/**
 * Global Helper Functions
 * Commonly used utility functions
 */

if (!function_exists('app')) {
    /**
     * Get application instance
     */
    function app(string $service = null)
    {
        $app = \App\Core\Application::getInstance();
        
        if ($service === null) {
            return $app;
        }
        
        return $app->get($service);
    }
}

if (!function_exists('config')) {
    /**
     * Get configuration value
     */
    function config(string $key = null, $default = null)
    {
        return app()->config($key, $default);
    }
}

if (!function_exists('env')) {
    /**
     * Get environment variable
     */
    function env(string $key, $default = null)
    {
        $value = $_ENV[$key] ?? getenv($key);
        
        if ($value === false) {
            return $default;
        }

        // Convert string booleans
        if (in_array(strtolower($value), ['true', 'false'])) {
            return strtolower($value) === 'true';
        }

        // Convert string nulls
        if (strtolower($value) === 'null') {
            return null;
        }

        // Convert numeric strings
        if (is_numeric($value)) {
            return strpos($value, '.') !== false ? (float) $value : (int) $value;
        }

        return $value;
    }
}

if (!function_exists('cache')) {
    /**
     * Get cache instance or cache value
     */
    function cache(string $key = null, $value = null, int $ttl = null)
    {
        $cache = app()->cache();
        
        if ($key === null) {
            return $cache;
        }
        
        if ($value !== null) {
            return $cache->put($key, $value, $ttl);
        }
        
        return $cache->get($key);
    }
}

if (!function_exists('session')) {
    /**
     * Get session instance or session value
     */
    function session(string $key = null, $value = null)
    {
        $session = app()->session();
        
        if ($key === null) {
            return $session;
        }
        
        if ($value !== null) {
            return $session->put($key, $value);
        }
        
        return $session->get($key);
    }
}

if (!function_exists('request')) {
    /**
     * Get current request instance
     */
    function request(): \App\Core\Http\Request
    {
        return \App\Core\Http\Request::createFromGlobals();
    }
}

if (!function_exists('response')) {
    /**
     * Create response instance
     */
    function response($content = '', int $status = 200, array $headers = []): \App\Core\Http\Response
    {
        return new \App\Core\Http\Response($content, $status, $headers);
    }
}

if (!function_exists('json')) {
    /**
     * Create JSON response
     */
    function json($data, int $status = 200, array $headers = []): \App\Core\Http\Response
    {
        return \App\Core\Http\Response::json($data, $status, $headers);
    }
}

if (!function_exists('redirect')) {
    /**
     * Create redirect response
     */
    function redirect(string $url, int $status = 302, array $headers = []): \App\Core\Http\Response
    {
        return \App\Core\Http\Response::redirect($url, $status, $headers);
    }
}

if (!function_exists('url')) {
    /**
     * Generate URL for named route
     */
    function url(string $name, array $parameters = []): string
    {
        return app()->router()->url($name, $parameters);
    }
}

if (!function_exists('asset')) {
    /**
     * Generate asset URL
     */
    function asset(string $path): string
    {
        $baseUrl = config('app.url', 'http://localhost');
        return rtrim($baseUrl, '/') . '/assets/' . ltrim($path, '/');
    }
}

if (!function_exists('csrf_token')) {
    /**
     * Get CSRF token
     */
    function csrf_token(): string
    {
        return app()->security()->generateCsrfToken();
    }
}

if (!function_exists('csrf_field')) {
    /**
     * Generate CSRF hidden input field
     */
    function csrf_field(): string
    {
        $token = csrf_token();
        return '<input type="hidden" name="_token" value="' . $token . '">';
    }
}

if (!function_exists('old')) {
    /**
     * Get old input value
     */
    function old(string $key, $default = null)
    {
        return session()->getFlash("old.{$key}", $default);
    }
}

if (!function_exists('flash')) {
    /**
     * Flash data to session
     */
    function flash(string $key, $value): void
    {
        session()->flash($key, $value);
    }
}

if (!function_exists('now')) {
    /**
     * Get current timestamp
     */
    function now(): int
    {
        return time();
    }
}

if (!function_exists('today')) {
    /**
     * Get today's date
     */
    function today(string $format = 'Y-m-d'): string
    {
        return date($format);
    }
}

if (!function_exists('str_random')) {
    /**
     * Generate random string
     */
    function str_random(int $length = 16): string
    {
        return bin2hex(random_bytes($length / 2));
    }
}

if (!function_exists('str_slug')) {
    /**
     * Generate URL-friendly slug
     */
    function str_slug(string $string, string $separator = '-'): string
    {
        $string = preg_replace('/[^\p{L}\p{Nd}]+/u', $separator, $string);
        $string = trim($string, $separator);
        return strtolower($string);
    }
}

if (!function_exists('str_limit')) {
    /**
     * Limit string length
     */
    function str_limit(string $string, int $limit = 100, string $end = '...'): string
    {
        if (mb_strlen($string) <= $limit) {
            return $string;
        }
        
        return mb_substr($string, 0, $limit) . $end;
    }
}

if (!function_exists('e')) {
    /**
     * Escape HTML entities
     */
    function e(string $value): string
    {
        return htmlspecialchars($value, ENT_QUOTES, 'UTF-8', false);
    }
}

if (!function_exists('dd')) {
    /**
     * Dump and die (for debugging)
     */
    function dd(...$vars): void
    {
        foreach ($vars as $var) {
            echo '<pre>';
            var_dump($var);
            echo '</pre>';
        }
        die();
    }
}

if (!function_exists('dump')) {
    /**
     * Dump variable (for debugging)
     */
    function dump(...$vars): void
    {
        foreach ($vars as $var) {
            echo '<pre>';
            var_dump($var);
            echo '</pre>';
        }
    }
}

if (!function_exists('logger')) {
    /**
     * Log message
     */
    function logger(string $message, string $level = 'info', array $context = []): void
    {
        $logFile = BASE_PATH . '/storage/logs/app.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $timestamp = date('Y-m-d H:i:s');
        $contextStr = !empty($context) ? ' ' . json_encode($context) : '';
        $logEntry = "[{$timestamp}] {$level}: {$message}{$contextStr}" . PHP_EOL;
        
        file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
}

if (!function_exists('abort')) {
    /**
     * Abort with HTTP status code
     */
    function abort(int $code, string $message = ''): void
    {
        http_response_code($code);
        
        if ($message) {
            echo $message;
        } else {
            switch ($code) {
                case 404:
                    echo 'Not Found';
                    break;
                case 403:
                    echo 'Forbidden';
                    break;
                case 500:
                    echo 'Internal Server Error';
                    break;
                default:
                    echo 'Error';
            }
        }
        
        exit;
    }
}

if (!function_exists('collect')) {
    /**
     * Create collection from array
     */
    function collect(array $items = []): array
    {
        return $items; // Simple implementation, can be enhanced with Collection class
    }
}

if (!function_exists('value')) {
    /**
     * Return the default value of the given value
     */
    function value($value)
    {
        return $value instanceof Closure ? $value() : $value;
    }
}

if (!function_exists('with')) {
    /**
     * Return the given value, optionally passed through the given callback
     */
    function with($value, callable $callback = null)
    {
        return is_null($callback) ? $value : $callback($value);
    }
}

if (!function_exists('tap')) {
    /**
     * Call the given callback with the given value then return the value
     */
    function tap($value, callable $callback)
    {
        $callback($value);
        return $value;
    }
}

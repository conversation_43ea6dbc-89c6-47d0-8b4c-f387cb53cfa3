<?php

namespace App\Modules\Public\Models;

class PageModel
{
    /**
     * Get page metadata
     */
    public static function getPageMeta(string $page): array
    {
        $metadata = [
            'home' => [
                'title' => 'JobSpace - Welcome',
                'description' => 'High-Performance Platform for Quiz, Social Media, E-commerce & Freelancing',
                'keywords' => 'jobspace, platform, quiz, social media, ecommerce, freelancing'
            ],
            'about' => [
                'title' => 'About - JobSpace',
                'description' => 'Learn more about JobSpace platform and its features',
                'keywords' => 'about jobspace, platform features, company info'
            ],
            'contact' => [
                'title' => 'Contact - JobSpace',
                'description' => 'Get in touch with JobSpace team',
                'keywords' => 'contact jobspace, support, help'
            ],
            'terms' => [
                'title' => 'Terms of Service - JobSpace',
                'description' => 'Terms and conditions for using JobSpace platform',
                'keywords' => 'terms of service, legal, conditions'
            ],
            'privacy' => [
                'title' => 'Privacy Policy - JobSpace',
                'description' => 'Privacy policy for JobSpace platform',
                'keywords' => 'privacy policy, data protection, privacy'
            ]
        ];

        return $metadata[$page] ?? [
            'title' => 'JobSpace',
            'description' => 'JobSpace Platform',
            'keywords' => 'jobspace'
        ];
    }

    /**
     * Get navigation menu
     */
    public static function getNavigation(): array
    {
        return [
            ['url' => '/jobspace/', 'title' => 'Home', 'active' => false],
            ['url' => '/jobspace/about', 'title' => 'About', 'active' => false],
            ['url' => '/jobspace/contact', 'title' => 'Contact', 'active' => false],
            ['url' => '/jobspace/terms', 'title' => 'Terms', 'active' => false],
            ['url' => '/jobspace/privacy', 'title' => 'Privacy', 'active' => false],
        ];
    }

    /**
     * Get footer links
     */
    public static function getFooterLinks(): array
    {
        return [
            'quick_links' => [
                ['url' => '/jobspace/', 'title' => 'Home'],
                ['url' => '/jobspace/about', 'title' => 'About'],
                ['url' => '/jobspace/contact', 'title' => 'Contact'],
            ],
            'legal' => [
                ['url' => '/jobspace/terms', 'title' => 'Terms of Service'],
                ['url' => '/jobspace/privacy', 'title' => 'Privacy Policy'],
            ]
        ];
    }

    /**
     * Get platform stats
     */
    public static function getPlatformStats(): array
    {
        return [
            ['value' => '50K+', 'label' => 'Concurrent Users', 'color' => 'blue'],
            ['value' => '<200ms', 'label' => 'Response Time', 'color' => 'green'],
            ['value' => '99.9%', 'label' => 'Uptime', 'color' => 'purple'],
            ['value' => '1M+', 'label' => 'Users Capacity', 'color' => 'yellow'],
        ];
    }

    /**
     * Get platform features
     */
    public static function getPlatformFeatures(): array
    {
        return [
            [
                'icon' => '🧠',
                'title' => 'Quiz System',
                'description' => '35 advanced features for creating and managing quizzes',
                'features' => ['Multiple question types', 'Real-time scoring', 'Analytics & reports']
            ],
            [
                'icon' => '📱',
                'title' => 'Social Media',
                'description' => '20 features for social networking platform',
                'features' => ['Posts & comments', 'Real-time feed', 'Follow system']
            ],
            [
                'icon' => '🛒',
                'title' => 'E-commerce',
                'description' => '15 features for online marketplace',
                'features' => ['Product catalog', 'Shopping cart', 'Payment gateway']
            ],
            [
                'icon' => '💼',
                'title' => 'Freelancing',
                'description' => '20 features for freelance marketplace',
                'features' => ['Job posting', 'Skill matching', 'Escrow system']
            ]
        ];
    }
}

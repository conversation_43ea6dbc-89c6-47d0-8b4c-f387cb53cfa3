<?php

namespace App\Core\Http;

/**
 * HTTP Request Class
 * Handles incoming HTTP requests with security and performance optimizations
 */
class Request
{
    private array $query;
    private array $request;
    private array $attributes;
    private array $cookies;
    private array $files;
    private array $server;
    private array $headers;
    private ?string $content = null;
    private array $routeParameters = [];

    public function __construct(
        array $query = [],
        array $request = [],
        array $attributes = [],
        array $cookies = [],
        array $files = [],
        array $server = [],
        $content = null
    ) {
        $this->query = $query;
        $this->request = $request;
        $this->attributes = $attributes;
        $this->cookies = $cookies;
        $this->files = $files;
        $this->server = $server;
        $this->content = $content;
        $this->headers = $this->parseHeaders();
    }

    /**
     * Create request from PHP globals
     */
    public static function createFromGlobals(): self
    {
        return new self(
            $_GET,
            $_POST,
            [],
            $_COOKIE,
            $_FILES,
            $_SERVER,
            file_get_contents('php://input')
        );
    }

    /**
     * Get request method
     */
    public function getMethod(): string
    {
        $method = strtoupper($this->server['REQUEST_METHOD'] ?? 'GET');
        
        // Check for method override
        if ($method === 'POST') {
            $override = $this->request['_method'] ?? $this->headers['X-HTTP-METHOD-OVERRIDE'] ?? null;
            if ($override) {
                $method = strtoupper($override);
            }
        }

        return $method;
    }

    /**
     * Get request URI
     */
    public function getUri(): string
    {
        return $this->server['REQUEST_URI'] ?? '/';
    }

    /**
     * Get path info
     */
    public function getPathInfo(): string
    {
        $uri = $this->getUri();
        $path = parse_url($uri, PHP_URL_PATH);

        // For XAMPP subdirectory setup, remove the base directory
        // e.g., /jobspace/test becomes /test
        $scriptName = $_SERVER['SCRIPT_NAME'] ?? '';
        $basePath = dirname($scriptName);

        if ($basePath !== '/' && strpos($path, $basePath) === 0) {
            $path = substr($path, strlen($basePath));
        }

        // Ensure path starts with /
        if (!$path || $path[0] !== '/') {
            $path = '/' . ltrim($path, '/');
        }

        return $path;
    }

    /**
     * Get query string
     */
    public function getQueryString(): ?string
    {
        return $this->server['QUERY_STRING'] ?? null;
    }

    /**
     * Get request scheme
     */
    public function getScheme(): string
    {
        return $this->isSecure() ? 'https' : 'http';
    }

    /**
     * Check if request is secure (HTTPS)
     */
    public function isSecure(): bool
    {
        return (
            (!empty($this->server['HTTPS']) && $this->server['HTTPS'] !== 'off') ||
            (!empty($this->server['HTTP_X_FORWARDED_PROTO']) && $this->server['HTTP_X_FORWARDED_PROTO'] === 'https') ||
            (!empty($this->server['HTTP_X_FORWARDED_SSL']) && $this->server['HTTP_X_FORWARDED_SSL'] === 'on') ||
            (!empty($this->server['SERVER_PORT']) && $this->server['SERVER_PORT'] == 443)
        );
    }

    /**
     * Get host
     */
    public function getHost(): string
    {
        return $this->server['HTTP_HOST'] ?? $this->server['SERVER_NAME'] ?? 'localhost';
    }

    /**
     * Get full URL
     */
    public function getUrl(): string
    {
        return $this->getScheme() . '://' . $this->getHost() . $this->getUri();
    }

    /**
     * Get client IP address
     */
    public function getClientIp(): string
    {
        $ipKeys = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_CLIENT_IP',            // Proxy
            'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
            'HTTP_X_FORWARDED',          // Proxy
            'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
            'HTTP_FORWARDED_FOR',        // Proxy
            'HTTP_FORWARDED',            // Proxy
            'REMOTE_ADDR'                // Standard
        ];

        foreach ($ipKeys as $key) {
            if (!empty($this->server[$key])) {
                $ips = explode(',', $this->server[$key]);
                $ip = trim($ips[0]);
                
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return $this->server['REMOTE_ADDR'] ?? '127.0.0.1';
    }

    /**
     * Get user agent
     */
    public function getUserAgent(): string
    {
        return $this->server['HTTP_USER_AGENT'] ?? '';
    }

    /**
     * Get input value
     */
    public function input(string $key = null, $default = null)
    {
        if ($key === null) {
            return array_merge($this->query, $this->request);
        }

        return $this->query[$key] ?? $this->request[$key] ?? $default;
    }

    /**
     * Get query parameter
     */
    public function query(string $key = null, $default = null)
    {
        if ($key === null) {
            return $this->query;
        }

        return $this->query[$key] ?? $default;
    }

    /**
     * Get POST parameter
     */
    public function post(string $key = null, $default = null)
    {
        if ($key === null) {
            return $this->request;
        }

        return $this->request[$key] ?? $default;
    }

    /**
     * Get header value
     */
    public function header(string $key, $default = null)
    {
        $key = strtolower(str_replace('_', '-', $key));
        return $this->headers[$key] ?? $default;
    }

    /**
     * Get all headers
     */
    public function headers(): array
    {
        return $this->headers;
    }

    /**
     * Get cookie value
     */
    public function cookie(string $key, $default = null)
    {
        return $this->cookies[$key] ?? $default;
    }

    /**
     * Get file upload
     */
    public function file(string $key)
    {
        return $this->files[$key] ?? null;
    }

    /**
     * Get request content
     */
    public function getContent(): ?string
    {
        return $this->content;
    }

    /**
     * Get JSON data
     */
    public function json(string $key = null, $default = null)
    {
        static $json = null;
        
        if ($json === null) {
            $json = json_decode($this->getContent(), true) ?: [];
        }

        if ($key === null) {
            return $json;
        }

        return $json[$key] ?? $default;
    }

    /**
     * Check if request expects JSON
     */
    public function expectsJson(): bool
    {
        return str_contains($this->header('accept', ''), 'application/json') ||
               str_contains($this->header('content-type', ''), 'application/json');
    }

    /**
     * Check if request is AJAX
     */
    public function isAjax(): bool
    {
        return $this->header('x-requested-with') === 'XMLHttpRequest';
    }

    /**
     * Check if request method matches
     */
    public function isMethod(string $method): bool
    {
        return $this->getMethod() === strtoupper($method);
    }

    /**
     * Set route parameters
     */
    public function setRouteParameters(array $parameters): void
    {
        $this->routeParameters = $parameters;
    }

    /**
     * Get route parameter
     */
    public function route(string $key = null, $default = null)
    {
        if ($key === null) {
            return $this->routeParameters;
        }

        return $this->routeParameters[$key] ?? $default;
    }

    /**
     * Parse headers from server variables
     */
    private function parseHeaders(): array
    {
        $headers = [];
        
        foreach ($this->server as $key => $value) {
            if (strpos($key, 'HTTP_') === 0) {
                $header = strtolower(str_replace('_', '-', substr($key, 5)));
                $headers[$header] = $value;
            }
        }

        return $headers;
    }

    /**
     * Get all input data
     */
    public function all(): array
    {
        return array_merge($this->query, $this->request, $this->routeParameters);
    }

    /**
     * Check if input key exists
     */
    public function has(string $key): bool
    {
        return isset($this->query[$key]) || isset($this->request[$key]);
    }

    /**
     * Get only specified keys
     */
    public function only(array $keys): array
    {
        $result = [];
        $input = $this->all();
        
        foreach ($keys as $key) {
            if (isset($input[$key])) {
                $result[$key] = $input[$key];
            }
        }

        return $result;
    }

    /**
     * Get all except specified keys
     */
    public function except(array $keys): array
    {
        $input = $this->all();
        
        foreach ($keys as $key) {
            unset($input[$key]);
        }

        return $input;
    }
}

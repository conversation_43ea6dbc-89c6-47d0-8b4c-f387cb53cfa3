<?php

namespace App\Core;

class Security
{
    /**
     * Generate CSRF token
     */
    public static function generateCSRFToken(): string
    {
        SessionManager::init();
        
        if (!SessionManager::has('csrf_token')) {
            $token = bin2hex(random_bytes(32));
            SessionManager::set('csrf_token', $token);
        }
        
        return SessionManager::get('csrf_token');
    }

    /**
     * Verify CSRF token
     */
    public static function verifyCSRFToken(string $token): bool
    {
        $sessionToken = SessionManager::get('csrf_token');
        
        if (!$sessionToken || !$token) {
            return false;
        }
        
        return hash_equals($sessionToken, $token);
    }

    /**
     * Get CSRF token input field
     */
    public static function getCSRFField(): string
    {
        $token = self::generateCSRFToken();
        $tokenName = $_ENV['CSRF_TOKEN_NAME'] ?? '_token';
        
        return "<input type=\"hidden\" name=\"{$tokenName}\" value=\"{$token}\">";
    }

    /**
     * Validate CSRF token from request
     */
    public static function validateCSRFFromRequest(): bool
    {
        $tokenName = $_ENV['CSRF_TOKEN_NAME'] ?? '_token';
        $headerName = $_ENV['CSRF_HEADER_NAME'] ?? 'X-CSRF-TOKEN';
        
        // Check POST data first, then headers
        $token = $_POST[$tokenName] ?? $_SERVER['HTTP_' . str_replace('-', '_', strtoupper($headerName))] ?? '';
        
        return self::verifyCSRFToken($token);
    }

    /**
     * Rate limiting check
     */
    public static function checkRateLimit(string $key, int $maxAttempts = 60, int $decayMinutes = 1): bool
    {
        if (!($_ENV['RATE_LIMIT_ENABLED'] ?? true)) {
            return true;
        }
        
        $maxAttempts = $_ENV['RATE_LIMIT_MAX_ATTEMPTS'] ?? $maxAttempts;
        $decayMinutes = $_ENV['RATE_LIMIT_DECAY_MINUTES'] ?? $decayMinutes;
        
        try {
            $now = time();
            $windowStart = $now - ($decayMinutes * 60);
            
            // Clean up old attempts
            Database::delete(
                'login_attempts',
                'created_at < ?',
                [date('Y-m-d H:i:s', $windowStart)]
            );
            
            // Count current attempts
            $result = Database::fetch(
                "SELECT COUNT(*) as count FROM login_attempts WHERE ip_address = ? AND created_at >= ?",
                [$key, date('Y-m-d H:i:s', $windowStart)]
            );
            
            $currentAttempts = $result['count'] ?? 0;
            
            if ($currentAttempts >= $maxAttempts) {
                return false;
            }
            
            // Record this attempt
            Database::insert('login_attempts', [
                'ip_address' => $key,
                'attempts' => 1,
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            return true;
            
        } catch (\Exception $e) {
            error_log("Rate limiting check failed: " . $e->getMessage());
            return true; // Allow on error
        }
    }

    /**
     * Check login attempts for specific email/IP
     */
    public static function checkLoginAttempts(string $email, string $ipAddress): array
    {
        try {
            $lockoutDuration = 15; // 15 minutes
            $maxAttempts = 5;
            
            // Check if IP is currently locked
            $ipLock = Database::fetch(
                "SELECT * FROM login_attempts WHERE ip_address = ? AND locked_until > NOW()",
                [$ipAddress]
            );
            
            if ($ipLock) {
                return [
                    'allowed' => false,
                    'reason' => 'IP temporarily locked',
                    'locked_until' => $ipLock['locked_until']
                ];
            }
            
            // Check email attempts in last hour
            $hourAgo = date('Y-m-d H:i:s', time() - 3600);
            $emailAttempts = Database::fetch(
                "SELECT COUNT(*) as count FROM login_attempts WHERE email = ? AND created_at >= ?",
                [$email, $hourAgo]
            );
            
            $emailCount = $emailAttempts['count'] ?? 0;
            
            // Check IP attempts in last hour
            $ipAttempts = Database::fetch(
                "SELECT COUNT(*) as count FROM login_attempts WHERE ip_address = ? AND created_at >= ?",
                [$ipAddress, $hourAgo]
            );
            
            $ipCount = $ipAttempts['count'] ?? 0;
            
            if ($emailCount >= $maxAttempts || $ipCount >= $maxAttempts) {
                // Lock the IP
                $lockedUntil = date('Y-m-d H:i:s', time() + ($lockoutDuration * 60));
                
                Database::query(
                    "INSERT INTO login_attempts (ip_address, email, attempts, locked_until, created_at) 
                     VALUES (?, ?, ?, ?, NOW()) 
                     ON DUPLICATE KEY UPDATE 
                     attempts = attempts + 1, locked_until = ?, updated_at = NOW()",
                    [$ipAddress, $email, $maxAttempts, $lockedUntil, $lockedUntil]
                );
                
                return [
                    'allowed' => false,
                    'reason' => 'Too many failed attempts',
                    'locked_until' => $lockedUntil
                ];
            }
            
            return ['allowed' => true];
            
        } catch (\Exception $e) {
            error_log("Login attempts check failed: " . $e->getMessage());
            return ['allowed' => true]; // Allow on error
        }
    }

    /**
     * Record failed login attempt
     */
    public static function recordFailedLogin(string $email, string $ipAddress): void
    {
        try {
            Database::insert('login_attempts', [
                'ip_address' => $ipAddress,
                'email' => $email,
                'attempts' => 1,
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            error_log("Failed to record login attempt: " . $e->getMessage());
        }
    }

    /**
     * Clear login attempts for successful login
     */
    public static function clearLoginAttempts(string $email, string $ipAddress): void
    {
        try {
            Database::delete('login_attempts', 'email = ? OR ip_address = ?', [$email, $ipAddress]);
        } catch (\Exception $e) {
            error_log("Failed to clear login attempts: " . $e->getMessage());
        }
    }

    /**
     * Sanitize input
     */
    public static function sanitizeInput(string $input): string
    {
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }

    /**
     * Validate email
     */
    public static function validateEmail(string $email): bool
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * Generate secure random string
     */
    public static function generateRandomString(int $length = 32): string
    {
        return bin2hex(random_bytes($length / 2));
    }

    /**
     * Hash password
     */
    public static function hashPassword(string $password): string
    {
        return password_hash($password, PASSWORD_DEFAULT);
    }

    /**
     * Verify password
     */
    public static function verifyPassword(string $password, string $hash): bool
    {
        return password_verify($password, $hash);
    }

    /**
     * Get client IP address
     */
    public static function getClientIP(): string
    {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                // Handle comma-separated IPs (from proxies)
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    }

    /**
     * Secure file upload validation
     */
    public static function validateFileUpload(array $file, array $allowedTypes = [], int $maxSize = 2048): array
    {
        $errors = [];
        
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $errors[] = 'File upload failed';
            return $errors;
        }
        
        // Check file size (in KB)
        if ($file['size'] > $maxSize * 1024) {
            $errors[] = "File size must be less than {$maxSize}KB";
        }
        
        // Check file type
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!empty($allowedTypes) && !in_array($extension, $allowedTypes)) {
            $errors[] = 'File type not allowed';
        }
        
        // Check MIME type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        $allowedMimes = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'pdf' => 'application/pdf'
        ];
        
        if (!empty($allowedTypes) && isset($allowedMimes[$extension])) {
            if ($mimeType !== $allowedMimes[$extension]) {
                $errors[] = 'File type mismatch';
            }
        }
        
        return $errors;
    }
}

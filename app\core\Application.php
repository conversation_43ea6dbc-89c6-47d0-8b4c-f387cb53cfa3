<?php

namespace App\Core;

use App\Core\Database\DatabaseManager;
use App\Core\Cache\CacheManager;
use App\Core\Security\SecurityManager;
use App\Core\Http\Router;
use App\Core\Http\Request;
use App\Core\Http\Response;
use App\Core\Container\Container;
use App\Core\Config\ConfigManager;
use App\Core\Session\SessionManager;
use Exception;

/**
 * Main Application Class
 * High-Performance Framework Core for 50K+ Concurrent Users
 */
class Application
{
    private static ?Application $instance = null;
    private Container $container;
    private ConfigManager $config;
    private DatabaseManager $database;
    private CacheManager $cache;
    private SecurityManager $security;
    private Router $router;
    private SessionManager $session;
    private bool $booted = false;

    private function __construct()
    {
        $this->container = new Container();
        $this->registerCoreServices();
    }

    public static function getInstance(): Application
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Boot the application
     */
    public function boot(): void
    {
        if ($this->booted) {
            return;
        }

        try {
            // Load configuration first
            $this->config = $this->container->get(ConfigManager::class);
            $this->config->load();

            // Initialize core services one by one with error handling
            try {
                $this->database = $this->container->get(DatabaseManager::class);
            } catch (Exception $e) {
                error_log("Database initialization failed: " . $e->getMessage());
                // Continue without database for now
            }

            try {
                $this->cache = $this->container->get(CacheManager::class);
            } catch (Exception $e) {
                error_log("Cache initialization failed: " . $e->getMessage());
                // Continue without cache for now
            }

            try {
                $this->security = $this->container->get(SecurityManager::class);
            } catch (Exception $e) {
                error_log("Security initialization failed: " . $e->getMessage());
                // Continue without security for now
            }

            try {
                $this->session = $this->container->get(SessionManager::class);
                $this->session->start();
            } catch (Exception $e) {
                error_log("Session initialization failed: " . $e->getMessage());
                // Continue without session for now
            }

            $this->router = $this->container->get(Router::class);

            // Load routes
            $this->loadRoutes();

            $this->booted = true;

        } catch (Exception $e) {
            $this->handleBootError($e);
        }
    }

    /**
     * Handle HTTP request
     */
    public function handleRequest(): Response
    {
        try {
            $request = Request::createFromGlobals();

            // Security checks (only if security is initialized)
            if ($this->security) {
                try {
                    $this->security->validateRequest($request);
                    $this->security->checkRateLimit($request);
                } catch (Exception $e) {
                    error_log("Security check failed: " . $e->getMessage());
                    // Continue without security checks for now
                }
            }

            // Route the request
            $response = $this->router->dispatch($request);

            return $response;

        } catch (Exception $e) {
            return $this->handleError($e);
        }
    }

    /**
     * Register core services in container
     */
    private function registerCoreServices(): void
    {
        // Configuration Manager
        $this->container->singleton(ConfigManager::class, function() {
            return new ConfigManager();
        });

        // Database Manager
        $this->container->singleton(DatabaseManager::class, function() {
            return new DatabaseManager($this->config);
        });

        // Cache Manager
        $this->container->singleton(CacheManager::class, function() {
            return new CacheManager($this->config);
        });

        // Security Manager
        $this->container->singleton(SecurityManager::class, function() {
            return new SecurityManager($this->config);
        });

        // Session Manager
        $this->container->singleton(SessionManager::class, function() {
            return new SessionManager($this->config);
        });

        // Router
        $this->container->singleton(Router::class, function() {
            return new Router($this->container);
        });
    }

    /**
     * Load application routes
     */
    private function loadRoutes(): void
    {
        // Load web routes
        $webRoutesFile = BASE_PATH . '/routes/web.php';
        if (file_exists($webRoutesFile)) {
            require $webRoutesFile;
        }

        // Load API routes
        $apiRoutesFile = BASE_PATH . '/routes/api.php';
        if (file_exists($apiRoutesFile)) {
            require $apiRoutesFile;
        }
    }

    /**
     * Handle boot errors
     */
    private function handleBootError(Exception $e): void
    {
        error_log("Application Boot Error: " . $e->getMessage());
        
        if ($this->config && $this->config->get('app.debug', false)) {
            throw $e;
        }
        
        http_response_code(500);
        echo "Application failed to start. Please try again later.";
        exit;
    }

    /**
     * Handle runtime errors
     */
    private function handleError(Exception $e): Response
    {
        error_log("Application Error: " . $e->getMessage());
        
        if ($this->config && $this->config->get('app.debug', false)) {
            return new Response(
                json_encode([
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]),
                500,
                ['Content-Type' => 'application/json']
            );
        }
        
        return new Response(
            json_encode(['error' => 'Internal Server Error']),
            500,
            ['Content-Type' => 'application/json']
        );
    }

    /**
     * Get service from container
     */
    public function get(string $service)
    {
        return $this->container->get($service);
    }

    /**
     * Get configuration
     */
    public function config(string $key = null, $default = null)
    {
        if ($key === null) {
            return $this->config;
        }
        return $this->config->get($key, $default);
    }

    /**
     * Get database instance
     */
    public function db(): DatabaseManager
    {
        return $this->database;
    }

    /**
     * Get cache instance
     */
    public function cache(): CacheManager
    {
        return $this->cache;
    }

    /**
     * Get security instance
     */
    public function security(): SecurityManager
    {
        return $this->security;
    }

    /**
     * Get router instance
     */
    public function router(): Router
    {
        return $this->router;
    }

    /**
     * Get session instance
     */
    public function session(): SessionManager
    {
        return $this->session;
    }

    /**
     * Shutdown the application
     */
    public function shutdown(): void
    {
        if ($this->database) {
            $this->database->disconnect();
        }
        
        if ($this->session) {
            $this->session->save();
        }
        
        if ($this->cache) {
            $this->cache->cleanup();
        }
    }

    /**
     * Prevent cloning
     */
    private function __clone() {}

    /**
     * Prevent unserialization
     */
    public function __wakeup()
    {
        throw new Exception("Cannot unserialize singleton");
    }
}

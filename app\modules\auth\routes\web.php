<?php

/**
 * Auth Module Routes
 * Authentication related routes
 */

use App\Modules\Auth\Controllers\AuthController;

// Initialize controller
$controller = new AuthController();

// Auth routes
return [
    // Registration routes
    '/auth/register' => [$controller, 'register'],
    '/auth/register/step2' => [$controller, 'registerStep2'],
    '/auth/process-step1' => [$controller, 'processStep1'],
    '/auth/process-step2' => [$controller, 'processStep2'],
    
    // Verification routes
    '/auth/verify-otp' => [$controller, 'verifyOTP'],
    '/auth/process-otp' => [$controller, 'processOTPVerification'],
    '/auth/resend-otp' => [$controller, 'resendOTP'],
    
    // AJAX routes
    '/auth/check-email' => [$controller, 'checkEmailAvailability'],
    '/auth/check-username' => [$controller, 'checkUsernameAvailability'],

    // Dashboard
    '/dashboard' => [$controller, 'dashboard'],
];

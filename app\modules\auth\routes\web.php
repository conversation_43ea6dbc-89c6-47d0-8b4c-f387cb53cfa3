<?php

/**
 * Auth Module Routes
 * Authentication related routes
 */

use App\Modules\Auth\Controllers\AuthController;

// Get router instance
$router = \App\Core\Application::getInstance()->router();

// Initialize controller
$controller = new AuthController();

// Login routes
$router->get('/auth/login', [$controller, 'login'])->name('auth.login');
$router->post('/auth/process-login', [$controller, 'processLogin'])->name('auth.process-login');

// Registration routes
$router->get('/auth/register', [$controller, 'register'])->name('auth.register');
$router->get('/auth/register/step2', [$controller, 'registerStep2'])->name('auth.register.step2');
$router->post('/auth/process-step1', [$controller, 'processStep1'])->name('auth.process-step1');
$router->post('/auth/process-step2', [$controller, 'processStep2'])->name('auth.process-step2');

// Verification routes
$router->get('/auth/verify-otp', [$controller, 'verifyOTP'])->name('auth.verify-otp');
$router->post('/auth/process-otp', [$controller, 'processOTPVerification'])->name('auth.process-otp');
$router->post('/auth/resend-otp', [$controller, 'resendOTP'])->name('auth.resend-otp');
$router->get('/auth/verify-email', [$controller, 'verifyEmail'])->name('auth.verify-email');

// Password reset routes
$router->get('/auth/forgot-password', [$controller, 'forgotPassword'])->name('auth.forgot-password');
$router->post('/auth/process-forgot-password', [$controller, 'processForgotPassword'])->name('auth.process-forgot-password');
$router->get('/auth/reset-password', [$controller, 'resetPassword'])->name('auth.reset-password');
$router->post('/auth/process-reset-password', [$controller, 'processResetPassword'])->name('auth.process-reset-password');

// AJAX routes
$router->post('/auth/check-email', [$controller, 'checkEmailAvailability'])->name('auth.check-email');
$router->post('/auth/check-username', [$controller, 'checkUsernameAvailability'])->name('auth.check-username');

// Dashboard
$router->get('/dashboard', [$controller, 'dashboard'])->name('dashboard');

// Logout
$router->get('/auth/logout', [$controller, 'logout'])->name('auth.logout');

<?php

namespace App\Core;

class ModuleManager
{
    private string $modulesPath;
    private array $loadedModules = [];

    public function __construct()
    {
        $this->modulesPath = BASE_PATH . '/app/modules';
    }

    /**
     * Check if a module exists and is valid
     */
    public function moduleExists(string $moduleName): bool
    {
        $modulePath = $this->modulesPath . '/' . strtolower($moduleName);
        
        // Check if module directory exists
        if (!is_dir($modulePath)) {
            return false;
        }

        // Check if module.json exists
        $moduleJsonPath = $modulePath . '/module.json';
        if (!file_exists($moduleJsonPath)) {
            return false;
        }

        // Check if controller exists
        $controllerPath = $modulePath . '/controllers/' . ucfirst($moduleName) . 'Controller.php';
        if (!file_exists($controllerPath)) {
            return false;
        }

        return true;
    }

    /**
     * Get module configuration
     */
    public function getModuleConfig(string $moduleName): ?array
    {
        if (!$this->moduleExists($moduleName)) {
            return null;
        }

        $moduleJsonPath = $this->modulesPath . '/' . strtolower($moduleName) . '/module.json';
        $config = json_decode(file_get_contents($moduleJsonPath), true);

        return $config ?: null;
    }

    /**
     * Load module routes
     */
    public function loadModuleRoutes(string $moduleName): ?array
    {
        if (!$this->moduleExists($moduleName)) {
            return null;
        }

        $routesPath = $this->modulesPath . '/' . strtolower($moduleName) . '/routes/web.php';
        
        if (!file_exists($routesPath)) {
            return null;
        }

        return include $routesPath;
    }

    /**
     * Get all available modules
     */
    public function getAvailableModules(): array
    {
        $modules = [];
        
        if (!is_dir($this->modulesPath)) {
            return $modules;
        }

        $directories = scandir($this->modulesPath);
        
        foreach ($directories as $dir) {
            if ($dir === '.' || $dir === '..') {
                continue;
            }
            
            $fullPath = $this->modulesPath . '/' . $dir;
            
            if (is_dir($fullPath) && $this->moduleExists($dir)) {
                $config = $this->getModuleConfig($dir);
                $modules[$dir] = $config;
            }
        }

        return $modules;
    }

    /**
     * Check if any modules are available
     */
    public function hasAnyModules(): bool
    {
        return count($this->getAvailableModules()) > 0;
    }

    /**
     * Get primary module (usually 'public')
     */
    public function getPrimaryModule(): ?string
    {
        // Check for public module first
        if ($this->moduleExists('public')) {
            return 'public';
        }

        // Get first available module
        $modules = $this->getAvailableModules();
        
        if (empty($modules)) {
            return null;
        }

        return array_key_first($modules);
    }

    /**
     * Load module dependencies
     */
    public function loadModuleDependencies(string $moduleName): void
    {
        $modulePath = $this->modulesPath . '/' . strtolower($moduleName);
        
        // Load controller
        $controllerPath = $modulePath . '/controllers/' . ucfirst($moduleName) . 'Controller.php';
        if (file_exists($controllerPath)) {
            require_once $controllerPath;
        }

        // Load models
        $modelsPath = $modulePath . '/models';
        if (is_dir($modelsPath)) {
            $modelFiles = glob($modelsPath . '/*.php');
            foreach ($modelFiles as $modelFile) {
                require_once $modelFile;
            }
        }

        // Load services
        $servicesPath = $modulePath . '/services';
        if (is_dir($servicesPath)) {
            $serviceFiles = glob($servicesPath . '/*.php');
            foreach ($serviceFiles as $serviceFile) {
                require_once $serviceFile;
            }
        }

        $this->loadedModules[] = $moduleName;
    }

    /**
     * Check if module is loaded
     */
    public function isModuleLoaded(string $moduleName): bool
    {
        return in_array($moduleName, $this->loadedModules);
    }

    /**
     * Get module status for debugging
     */
    public function getModuleStatus(): array
    {
        return [
            'modules_path' => $this->modulesPath,
            'modules_path_exists' => is_dir($this->modulesPath),
            'available_modules' => $this->getAvailableModules(),
            'loaded_modules' => $this->loadedModules,
            'has_modules' => $this->hasAnyModules(),
            'primary_module' => $this->getPrimaryModule()
        ];
    }
}

<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Fixing Authentication System</h1>";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=jobspace', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Database connected<br>";
    
    // 1. Create verification_tokens table if not exists
    echo "<h2>1. Creating verification_tokens table</h2>";
    $sql = "
    CREATE TABLE IF NOT EXISTS verification_tokens (
        id INT AUTO_INCREMENT PRIMARY KEY,
        email VARCHAR(255) NOT NULL,
        token VARCHAR(255) NOT NULL,
        otp VARCHAR(6) NOT NULL,
        user_data LONGTEXT NOT NULL,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_email (email),
        INDEX idx_token (token)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ";
    $pdo->exec($sql);
    echo "✅ verification_tokens table ready<br>";
    
    // 2. Check and fix users table structure
    echo "<h2>2. Checking users table structure</h2>";
    $stmt = $pdo->query("DESCRIBE users");
    $columns = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $columns[] = $row['Field'];
    }
    
    // Add missing columns
    $columnsToAdd = [
        'email_verified' => "ALTER TABLE users ADD COLUMN email_verified BOOLEAN DEFAULT FALSE",
        'referral_code_generated' => "ALTER TABLE users ADD COLUMN referral_code_generated VARCHAR(50) NULL"
    ];
    
    foreach ($columnsToAdd as $column => $sql) {
        if (!in_array($column, $columns)) {
            echo "Adding column: $column<br>";
            $pdo->exec($sql);
            echo "✅ Column $column added<br>";
        } else {
            echo "✅ Column $column exists<br>";
        }
    }
    
    // 3. Test insert into verification_tokens
    echo "<h2>3. Testing verification_tokens table</h2>";
    $testData = [
        'email' => '<EMAIL>',
        'token' => 'test_token_' . time(),
        'otp' => '123456',
        'user_data' => json_encode(['test' => 'data']),
        'expires_at' => date('Y-m-d H:i:s', time() + 600)
    ];
    
    $stmt = $pdo->prepare("
        INSERT INTO verification_tokens (email, token, otp, user_data, expires_at) 
        VALUES (?, ?, ?, ?, ?)
    ");
    $stmt->execute([
        $testData['email'],
        $testData['token'],
        $testData['otp'],
        $testData['user_data'],
        $testData['expires_at']
    ]);
    echo "✅ Test data inserted into verification_tokens<br>";
    
    // Clean up test data
    $pdo->exec("DELETE FROM verification_tokens WHERE email = '<EMAIL>'");
    echo "✅ Test data cleaned up<br>";
    
    // 4. Test users table insert
    echo "<h2>4. Testing users table</h2>";
    $testUser = [
        'username' => 'testuser_' . time(),
        'email' => 'testuser_' . time() . '@example.com',
        'password' => password_hash('password123', PASSWORD_DEFAULT),
        'first_name' => 'Test',
        'last_name' => 'User',
        'phone' => '01700000000',
        'date_of_birth' => '1990-01-01',
        'gender' => 'male',
        'role' => 'user',
        'status' => 'active',
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ];
    
    $fields = implode(', ', array_keys($testUser));
    $placeholders = ':' . implode(', :', array_keys($testUser));
    
    $stmt = $pdo->prepare("INSERT INTO users ($fields) VALUES ($placeholders)");
    $stmt->execute($testUser);
    $userId = $pdo->lastInsertId();
    echo "✅ Test user created with ID: $userId<br>";
    
    // Clean up test user
    $pdo->exec("DELETE FROM users WHERE id = $userId");
    echo "✅ Test user cleaned up<br>";
    
    echo "<h2>✅ Authentication System Fixed!</h2>";
    echo "<p>All database tables are ready and working properly.</p>";
    echo "<a href='/jobspace/auth/register'>Test Registration</a><br>";
    echo "<a href='/jobspace/test_registration.php'>Run Full Test</a>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
    echo "Stack trace: <pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<?php
namespace App\Helpers;

class UrlHelper {
    public static function baseUrl(string $path = ''): string {
        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';

        // /jobspace is the base path for the application
        $scriptName = dirname($_SERVER['SCRIPT_NAME']);
        $basePath = rtrim($scriptName, '/');
        $path = '/' . ltrim($path, '/');
        return $protocol . '://' . $host . $basePath . $path;
    }
}

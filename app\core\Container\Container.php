<?php

namespace App\Core\Container;

use Closure;
use Exception;
use ReflectionClass;
use ReflectionParameter;

/**
 * Dependency Injection Container
 * High-performance container for service management
 */
class Container
{
    private array $bindings = [];
    private array $instances = [];
    private array $singletons = [];

    /**
     * Bind a service to the container
     */
    public function bind(string $abstract, $concrete = null, bool $shared = false): void
    {
        if ($concrete === null) {
            $concrete = $abstract;
        }

        $this->bindings[$abstract] = [
            'concrete' => $concrete,
            'shared' => $shared
        ];
    }

    /**
     * Bind a singleton service
     */
    public function singleton(string $abstract, $concrete = null): void
    {
        $this->bind($abstract, $concrete, true);
    }

    /**
     * Bind an existing instance
     */
    public function instance(string $abstract, $instance): void
    {
        $this->instances[$abstract] = $instance;
    }

    /**
     * Resolve a service from the container
     */
    public function get(string $abstract)
    {
        // Return existing instance if available
        if (isset($this->instances[$abstract])) {
            return $this->instances[$abstract];
        }

        // Check if it's a singleton and already resolved
        if (isset($this->singletons[$abstract])) {
            return $this->singletons[$abstract];
        }

        // Resolve the service
        $instance = $this->resolve($abstract);

        // Store singleton instances
        if (isset($this->bindings[$abstract]) && $this->bindings[$abstract]['shared']) {
            $this->singletons[$abstract] = $instance;
        }

        return $instance;
    }

    /**
     * Resolve a service
     */
    private function resolve(string $abstract)
    {
        // Check if binding exists
        if (isset($this->bindings[$abstract])) {
            $concrete = $this->bindings[$abstract]['concrete'];
            
            if ($concrete instanceof Closure) {
                return $concrete($this);
            }
            
            return $this->build($concrete);
        }

        // Try to auto-resolve
        return $this->build($abstract);
    }

    /**
     * Build a class instance
     */
    private function build(string $concrete)
    {
        try {
            $reflector = new ReflectionClass($concrete);
        } catch (Exception $e) {
            throw new ContainerException("Target class [$concrete] does not exist.", 0, $e);
        }

        if (!$reflector->isInstantiable()) {
            throw new ContainerException("Target [$concrete] is not instantiable.");
        }

        $constructor = $reflector->getConstructor();

        if ($constructor === null) {
            return new $concrete;
        }

        $dependencies = $this->resolveDependencies($constructor->getParameters());

        return $reflector->newInstanceArgs($dependencies);
    }

    /**
     * Resolve constructor dependencies
     */
    private function resolveDependencies(array $parameters): array
    {
        $dependencies = [];

        foreach ($parameters as $parameter) {
            $dependency = $this->resolveDependency($parameter);
            $dependencies[] = $dependency;
        }

        return $dependencies;
    }

    /**
     * Resolve a single dependency
     */
    private function resolveDependency(ReflectionParameter $parameter)
    {
        $type = $parameter->getType();

        if ($type === null) {
            if ($parameter->isDefaultValueAvailable()) {
                return $parameter->getDefaultValue();
            }
            
            throw new ContainerException(
                "Cannot resolve parameter [{$parameter->getName()}] without type hint."
            );
        }

        $typeName = $type->getName();

        // Handle built-in types
        if ($type->isBuiltin()) {
            if ($parameter->isDefaultValueAvailable()) {
                return $parameter->getDefaultValue();
            }
            
            throw new ContainerException(
                "Cannot resolve built-in type [{$typeName}] for parameter [{$parameter->getName()}]."
            );
        }

        // Resolve class dependency
        try {
            return $this->get($typeName);
        } catch (Exception $e) {
            if ($parameter->isDefaultValueAvailable()) {
                return $parameter->getDefaultValue();
            }
            
            if ($parameter->allowsNull()) {
                return null;
            }
            
            throw new ContainerException(
                "Cannot resolve dependency [{$typeName}] for parameter [{$parameter->getName()}].",
                0,
                $e
            );
        }
    }

    /**
     * Check if service is bound
     */
    public function bound(string $abstract): bool
    {
        return isset($this->bindings[$abstract]) || isset($this->instances[$abstract]);
    }

    /**
     * Remove a binding
     */
    public function forget(string $abstract): void
    {
        unset($this->bindings[$abstract], $this->instances[$abstract], $this->singletons[$abstract]);
    }

    /**
     * Get all bindings
     */
    public function getBindings(): array
    {
        return $this->bindings;
    }

    /**
     * Flush all bindings and instances
     */
    public function flush(): void
    {
        $this->bindings = [];
        $this->instances = [];
        $this->singletons = [];
    }

    /**
     * Call a method with dependency injection
     */
    public function call($callback, array $parameters = [])
    {
        if (is_string($callback) && strpos($callback, '@') !== false) {
            [$class, $method] = explode('@', $callback);
            $callback = [$this->get($class), $method];
        }

        if (is_array($callback)) {
            [$instance, $method] = $callback;
            $reflector = new ReflectionClass($instance);
            $methodReflector = $reflector->getMethod($method);
            $dependencies = $this->resolveDependencies($methodReflector->getParameters());
            
            return $methodReflector->invokeArgs($instance, array_merge($dependencies, $parameters));
        }

        if ($callback instanceof Closure) {
            $reflector = new \ReflectionFunction($callback);
            $dependencies = $this->resolveDependencies($reflector->getParameters());
            
            return $callback(...array_merge($dependencies, $parameters));
        }

        throw new ContainerException("Invalid callback provided.");
    }

    /**
     * Magic method to get services
     */
    public function __get(string $name)
    {
        return $this->get($name);
    }

    /**
     * Magic method to check if service exists
     */
    public function __isset(string $name): bool
    {
        return $this->bound($name);
    }
}

/**
 * Container Exception
 */
class ContainerException extends Exception
{
    //
}

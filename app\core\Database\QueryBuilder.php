<?php

namespace App\Core\Database;

/**
 * Query Builder
 * Fluent interface for building SQL queries
 */
class QueryBuilder
{
    private string $table = '';
    private array $select = ['*'];
    private array $joins = [];
    private array $wheres = [];
    private array $orders = [];
    private array $groups = [];
    private array $havings = [];
    private ?int $limitCount = null;
    private ?int $offsetCount = null;
    private ?DatabaseManager $connection = null;

    /**
     * Set table name
     */
    public function table(string $table): self
    {
        $this->table = $table;
        return $this;
    }

    /**
     * Set database connection
     */
    public function setConnection(DatabaseManager $connection): self
    {
        $this->connection = $connection;
        return $this;
    }

    /**
     * Select columns
     */
    public function select(array $columns = ['*']): self
    {
        $this->select = $columns;
        return $this;
    }

    /**
     * Add where condition
     */
    public function where(string $column, $operator = null, $value = null, string $boolean = 'AND'): self
    {
        if (func_num_args() === 2) {
            $value = $operator;
            $operator = '=';
        }

        $this->wheres[] = [
            'type' => 'basic',
            'column' => $column,
            'operator' => $operator,
            'value' => $value,
            'boolean' => $boolean
        ];

        return $this;
    }

    /**
     * Add OR where condition
     */
    public function orWhere(string $column, $operator = null, $value = null): self
    {
        return $this->where($column, $operator, $value, 'OR');
    }

    /**
     * Where IN condition
     */
    public function whereIn(string $column, array $values, string $boolean = 'AND'): self
    {
        $this->wheres[] = [
            'type' => 'in',
            'column' => $column,
            'values' => $values,
            'boolean' => $boolean
        ];

        return $this;
    }

    /**
     * Where NOT IN condition
     */
    public function whereNotIn(string $column, array $values, string $boolean = 'AND'): self
    {
        $this->wheres[] = [
            'type' => 'not_in',
            'column' => $column,
            'values' => $values,
            'boolean' => $boolean
        ];

        return $this;
    }

    /**
     * Where NULL condition
     */
    public function whereNull(string $column, string $boolean = 'AND'): self
    {
        $this->wheres[] = [
            'type' => 'null',
            'column' => $column,
            'boolean' => $boolean
        ];

        return $this;
    }

    /**
     * Where NOT NULL condition
     */
    public function whereNotNull(string $column, string $boolean = 'AND'): self
    {
        $this->wheres[] = [
            'type' => 'not_null',
            'column' => $column,
            'boolean' => $boolean
        ];

        return $this;
    }

    /**
     * Where BETWEEN condition
     */
    public function whereBetween(string $column, array $values, string $boolean = 'AND'): self
    {
        $this->wheres[] = [
            'type' => 'between',
            'column' => $column,
            'values' => $values,
            'boolean' => $boolean
        ];

        return $this;
    }

    /**
     * Where LIKE condition
     */
    public function whereLike(string $column, string $value, string $boolean = 'AND'): self
    {
        return $this->where($column, 'LIKE', $value, $boolean);
    }

    /**
     * Add JOIN
     */
    public function join(string $table, string $first, string $operator, string $second, string $type = 'INNER'): self
    {
        $this->joins[] = [
            'type' => $type,
            'table' => $table,
            'first' => $first,
            'operator' => $operator,
            'second' => $second
        ];

        return $this;
    }

    /**
     * Add LEFT JOIN
     */
    public function leftJoin(string $table, string $first, string $operator, string $second): self
    {
        return $this->join($table, $first, $operator, $second, 'LEFT');
    }

    /**
     * Add RIGHT JOIN
     */
    public function rightJoin(string $table, string $first, string $operator, string $second): self
    {
        return $this->join($table, $first, $operator, $second, 'RIGHT');
    }

    /**
     * Add ORDER BY
     */
    public function orderBy(string $column, string $direction = 'ASC'): self
    {
        $this->orders[] = [
            'column' => $column,
            'direction' => strtoupper($direction)
        ];

        return $this;
    }

    /**
     * Add GROUP BY
     */
    public function groupBy(string $column): self
    {
        $this->groups[] = $column;
        return $this;
    }

    /**
     * Add HAVING
     */
    public function having(string $column, string $operator, $value): self
    {
        $this->havings[] = [
            'column' => $column,
            'operator' => $operator,
            'value' => $value
        ];

        return $this;
    }

    /**
     * Set LIMIT
     */
    public function limit(int $count): self
    {
        $this->limitCount = $count;
        return $this;
    }

    /**
     * Set OFFSET
     */
    public function offset(int $count): self
    {
        $this->offsetCount = $count;
        return $this;
    }

    /**
     * Paginate results
     */
    public function paginate(int $page, int $perPage): self
    {
        $this->limit($perPage);
        $this->offset(($page - 1) * $perPage);
        return $this;
    }

    /**
     * Execute SELECT query
     */
    public function get(): array
    {
        $sql = $this->buildSelectQuery();
        $bindings = $this->getBindings();
        
        if ($this->connection) {
            return $this->connection->query($sql, $bindings);
        }
        
        throw new \Exception("No database connection set");
    }

    /**
     * Get first result
     */
    public function first(): ?array
    {
        $this->limit(1);
        $results = $this->get();
        return $results[0] ?? null;
    }

    /**
     * Get count
     */
    public function count(): int
    {
        $originalSelect = $this->select;
        $this->select = ['COUNT(*) as count'];
        
        $result = $this->first();
        $this->select = $originalSelect;
        
        return (int) ($result['count'] ?? 0);
    }

    /**
     * Check if records exist
     */
    public function exists(): bool
    {
        return $this->count() > 0;
    }

    /**
     * Build SELECT query
     */
    public function buildSelectQuery(): string
    {
        $sql = 'SELECT ' . implode(', ', $this->select);
        $sql .= ' FROM ' . $this->table;
        
        if (!empty($this->joins)) {
            foreach ($this->joins as $join) {
                $sql .= " {$join['type']} JOIN {$join['table']} ON {$join['first']} {$join['operator']} {$join['second']}";
            }
        }
        
        if (!empty($this->wheres)) {
            $sql .= ' WHERE ' . $this->buildWhereClause();
        }
        
        if (!empty($this->groups)) {
            $sql .= ' GROUP BY ' . implode(', ', $this->groups);
        }
        
        if (!empty($this->havings)) {
            $sql .= ' HAVING ' . $this->buildHavingClause();
        }
        
        if (!empty($this->orders)) {
            $orderClauses = [];
            foreach ($this->orders as $order) {
                $orderClauses[] = "{$order['column']} {$order['direction']}";
            }
            $sql .= ' ORDER BY ' . implode(', ', $orderClauses);
        }
        
        if ($this->limitCount !== null) {
            $sql .= ' LIMIT ' . $this->limitCount;
        }
        
        if ($this->offsetCount !== null) {
            $sql .= ' OFFSET ' . $this->offsetCount;
        }
        
        return $sql;
    }

    /**
     * Build INSERT query
     */
    public function insert(string $table, array $data): string
    {
        $columns = implode(', ', array_keys($data));
        $placeholders = implode(', ', array_fill(0, count($data), '?'));
        
        return "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
    }

    /**
     * Build UPDATE query
     */
    public function update(string $table, array $data, array $where): string
    {
        $setClauses = [];
        foreach (array_keys($data) as $column) {
            $setClauses[] = "{$column} = ?";
        }
        
        $whereClauses = [];
        foreach (array_keys($where) as $column) {
            $whereClauses[] = "{$column} = ?";
        }
        
        $sql = "UPDATE {$table} SET " . implode(', ', $setClauses);
        if (!empty($whereClauses)) {
            $sql .= ' WHERE ' . implode(' AND ', $whereClauses);
        }
        
        return $sql;
    }

    /**
     * Build DELETE query
     */
    public function delete(string $table, array $where): string
    {
        $whereClauses = [];
        foreach (array_keys($where) as $column) {
            $whereClauses[] = "{$column} = ?";
        }
        
        $sql = "DELETE FROM {$table}";
        if (!empty($whereClauses)) {
            $sql .= ' WHERE ' . implode(' AND ', $whereClauses);
        }
        
        return $sql;
    }

    /**
     * Build WHERE clause
     */
    private function buildWhereClause(): string
    {
        $clauses = [];
        
        foreach ($this->wheres as $index => $where) {
            $boolean = $index === 0 ? '' : " {$where['boolean']} ";
            
            switch ($where['type']) {
                case 'basic':
                    $clauses[] = $boolean . "{$where['column']} {$where['operator']} ?";
                    break;
                case 'in':
                    $placeholders = implode(', ', array_fill(0, count($where['values']), '?'));
                    $clauses[] = $boolean . "{$where['column']} IN ({$placeholders})";
                    break;
                case 'not_in':
                    $placeholders = implode(', ', array_fill(0, count($where['values']), '?'));
                    $clauses[] = $boolean . "{$where['column']} NOT IN ({$placeholders})";
                    break;
                case 'null':
                    $clauses[] = $boolean . "{$where['column']} IS NULL";
                    break;
                case 'not_null':
                    $clauses[] = $boolean . "{$where['column']} IS NOT NULL";
                    break;
                case 'between':
                    $clauses[] = $boolean . "{$where['column']} BETWEEN ? AND ?";
                    break;
            }
        }
        
        return implode('', $clauses);
    }

    /**
     * Build HAVING clause
     */
    private function buildHavingClause(): string
    {
        $clauses = [];
        
        foreach ($this->havings as $having) {
            $clauses[] = "{$having['column']} {$having['operator']} ?";
        }
        
        return implode(' AND ', $clauses);
    }

    /**
     * Get query bindings
     */
    private function getBindings(): array
    {
        $bindings = [];
        
        foreach ($this->wheres as $where) {
            switch ($where['type']) {
                case 'basic':
                    $bindings[] = $where['value'];
                    break;
                case 'in':
                case 'not_in':
                    $bindings = array_merge($bindings, $where['values']);
                    break;
                case 'between':
                    $bindings = array_merge($bindings, $where['values']);
                    break;
            }
        }
        
        foreach ($this->havings as $having) {
            $bindings[] = $having['value'];
        }
        
        return $bindings;
    }

    /**
     * Reset query builder
     */
    public function reset(): self
    {
        $this->select = ['*'];
        $this->joins = [];
        $this->wheres = [];
        $this->orders = [];
        $this->groups = [];
        $this->havings = [];
        $this->limitCount = null;
        $this->offsetCount = null;
        
        return $this;
    }
}

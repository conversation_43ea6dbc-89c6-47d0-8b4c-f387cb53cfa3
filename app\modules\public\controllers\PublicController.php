<?php

namespace App\Modules\Public\Controllers;

use App\Modules\Public\Services\PageService;
use App\Modules\Public\Models\PageModel;

class PublicController
{
    private PageService $pageService;

    public function __construct()
    {
        $this->pageService = new PageService();
    }

    public function home()
    {
        $data = [
            'stats' => PageModel::getPlatformStats(),
            'features' => PageModel::getPlatformFeatures()
        ];

        return $this->pageService->renderPage('home', $data);
    }

    public function about()
    {
        return $this->pageService->renderPage('about');
    }

    public function contact()
    {
        $data = [];

        // Handle contact form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $formData = [
                'name' => $_POST['name'] ?? '',
                'email' => $_POST['email'] ?? '',
                'message' => $_POST['message'] ?? ''
            ];

            $result = $this->pageService->processContactForm($formData);
            $data['form_result'] = $result;
        }

        return $this->pageService->renderPage('contact', $data);
    }

    public function terms()
    {
        return $this->pageService->renderPage('terms');
    }

    public function privacy()
    {
        return $this->pageService->renderPage('privacy');
    }
}

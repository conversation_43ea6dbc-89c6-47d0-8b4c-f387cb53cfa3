<?php
class BaseController
{
    protected function view($module, $viewFile, $data = [])
    {
        // ভিউ ফাইলের সম্পূর্ণ পাথ ঠিক করে দিচ্ছি
        $fullPath = __DIR__ . "/../modules/$module/views/$viewFile";

        if (!file_exists($fullPath)) {
            die("View file not found: $fullPath");
        }

        // ডাটা ভিউ ফাইলে পাঠানোর জন্য এক্সট্র্যাক্ট করি
        extract($data);

        require $fullPath;
    }

    protected function redirect($url)
    {
        // Base URL থেকে relative URL তৈরি করি
        $baseUrl = rtrim(dirname($_SERVER['SCRIPT_NAME']), '/');
        $fullUrl = $baseUrl . $url;

        header("Location: $fullUrl");
        exit;
    }
}

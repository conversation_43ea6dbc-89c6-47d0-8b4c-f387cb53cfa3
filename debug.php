<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>JobSpace Debug Info</h1>";

echo "<h2>PHP Info</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Extensions: " . implode(', ', get_loaded_extensions()) . "<br>";

echo "<h2>Database Test</h2>";
try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=jobspace', 'root', '');
    echo "✅ Database connection successful!<br>";
    
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "Tables: " . implode(', ', $tables) . "<br>";
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

echo "<h2>File System</h2>";
echo "Current directory: " . __DIR__ . "<br>";
echo "Base path exists: " . (file_exists(__DIR__ . '/app') ? 'Yes' : 'No') . "<br>";
echo "Vendor exists: " . (file_exists(__DIR__ . '/vendor') ? 'Yes' : 'No') . "<br>";

echo "<h2>Session Test</h2>";
session_start();
$_SESSION['test'] = 'working';
echo "Session working: " . ($_SESSION['test'] === 'working' ? 'Yes' : 'No') . "<br>";

echo "<h2>Environment</h2>";
if (file_exists('.env')) {
    echo ".env file exists<br>";
    $env = file_get_contents('.env');
    echo "DB_DATABASE: " . ($_ENV['DB_DATABASE'] ?? 'Not set') . "<br>";
} else {
    echo ".env file not found<br>";
}
?>

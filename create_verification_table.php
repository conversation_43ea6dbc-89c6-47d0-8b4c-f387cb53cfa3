<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "Creating verification_tokens table...\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=jobspace', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $sql = "
    CREATE TABLE IF NOT EXISTS verification_tokens (
        id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        email VARCHAR(255) NOT NULL,
        token VARCHAR(255) NOT NULL,
        otp VARCHAR(10) NOT NULL,
        user_data TEXT NOT NULL,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        INDEX idx_email (email),
        INDEX idx_token (token),
        INDEX idx_expires_at (expires_at),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($sql);
    echo "✅ verification_tokens table created successfully!\n";
    
    // Check if table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'verification_tokens'");
    if ($stmt->rowCount() > 0) {
        echo "✅ Table verification confirmed!\n";
        
        // Show table structure
        $stmt = $pdo->query("DESCRIBE verification_tokens");
        echo "\nTable structure:\n";
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "- {$row['Field']}: {$row['Type']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

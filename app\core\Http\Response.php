<?php

namespace App\Core\Http;

/**
 * HTTP Response Class
 * Handles HTTP responses with performance optimizations
 */
class Response
{
    private string $content;
    private int $statusCode;
    private array $headers;
    private string $version;

    public function __construct(string $content = '', int $status = 200, array $headers = [])
    {
        $this->content = $content;
        $this->statusCode = $status;
        $this->headers = $headers;
        $this->version = '1.1';
    }

    /**
     * Create JSON response
     */
    public static function json($data, int $status = 200, array $headers = []): self
    {
        $headers['Content-Type'] = 'application/json';
        
        return new self(
            json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES),
            $status,
            $headers
        );
    }

    /**
     * Create redirect response
     */
    public static function redirect(string $url, int $status = 302, array $headers = []): self
    {
        $headers['Location'] = $url;
        return new self('', $status, $headers);
    }

    /**
     * Create view response
     */
    public static function view(string $view, array $data = [], int $status = 200, array $headers = []): self
    {
        // This will be implemented when we create the View system
        $content = "View: {$view}"; // Placeholder
        return new self($content, $status, $headers);
    }

    /**
     * Create download response
     */
    public static function download(string $file, string $name = null, array $headers = []): self
    {
        if (!file_exists($file)) {
            throw new \Exception("File not found: {$file}");
        }

        $name = $name ?: basename($file);
        $headers = array_merge($headers, [
            'Content-Type' => mime_content_type($file) ?: 'application/octet-stream',
            'Content-Disposition' => 'attachment; filename="' . $name . '"',
            'Content-Length' => filesize($file),
            'Cache-Control' => 'no-cache, must-revalidate',
            'Expires' => '0'
        ]);

        return new self(file_get_contents($file), 200, $headers);
    }

    /**
     * Set response content
     */
    public function setContent(string $content): self
    {
        $this->content = $content;
        return $this;
    }

    /**
     * Get response content
     */
    public function getContent(): string
    {
        return $this->content;
    }

    /**
     * Set status code
     */
    public function setStatusCode(int $code): self
    {
        $this->statusCode = $code;
        return $this;
    }

    /**
     * Get status code
     */
    public function getStatusCode(): int
    {
        return $this->statusCode;
    }

    /**
     * Set header
     */
    public function setHeader(string $name, string $value): self
    {
        $this->headers[$name] = $value;
        return $this;
    }

    /**
     * Get header
     */
    public function getHeader(string $name): ?string
    {
        return $this->headers[$name] ?? null;
    }

    /**
     * Set multiple headers
     */
    public function setHeaders(array $headers): self
    {
        $this->headers = array_merge($this->headers, $headers);
        return $this;
    }

    /**
     * Get all headers
     */
    public function getHeaders(): array
    {
        return $this->headers;
    }

    /**
     * Remove header
     */
    public function removeHeader(string $name): self
    {
        unset($this->headers[$name]);
        return $this;
    }

    /**
     * Check if header exists
     */
    public function hasHeader(string $name): bool
    {
        return isset($this->headers[$name]);
    }

    /**
     * Set cookie
     */
    public function setCookie(
        string $name,
        string $value,
        int $expire = 0,
        string $path = '/',
        string $domain = '',
        bool $secure = false,
        bool $httpOnly = true
    ): self {
        $cookie = [
            'name' => $name,
            'value' => $value,
            'expire' => $expire,
            'path' => $path,
            'domain' => $domain,
            'secure' => $secure,
            'httpOnly' => $httpOnly
        ];

        $this->headers['Set-Cookie'][] = $this->buildCookieHeader($cookie);
        return $this;
    }

    /**
     * Set HTTP version
     */
    public function setVersion(string $version): self
    {
        $this->version = $version;
        return $this;
    }

    /**
     * Get HTTP version
     */
    public function getVersion(): string
    {
        return $this->version;
    }

    /**
     * Send response to client
     */
    public function send(): void
    {
        $this->sendHeaders();
        $this->sendContent();
    }

    /**
     * Send headers
     */
    public function sendHeaders(): void
    {
        if (headers_sent()) {
            return;
        }

        // Send status line
        header(sprintf('HTTP/%s %s %s', $this->version, $this->statusCode, $this->getStatusText()));

        // Send headers
        foreach ($this->headers as $name => $value) {
            if (is_array($value)) {
                foreach ($value as $v) {
                    header("{$name}: {$v}", false);
                }
            } else {
                header("{$name}: {$value}");
            }
        }
    }

    /**
     * Send content
     */
    public function sendContent(): void
    {
        echo $this->content;
    }

    /**
     * Get status text for code
     */
    private function getStatusText(): string
    {
        $statusTexts = [
            200 => 'OK',
            201 => 'Created',
            202 => 'Accepted',
            204 => 'No Content',
            301 => 'Moved Permanently',
            302 => 'Found',
            304 => 'Not Modified',
            400 => 'Bad Request',
            401 => 'Unauthorized',
            403 => 'Forbidden',
            404 => 'Not Found',
            405 => 'Method Not Allowed',
            422 => 'Unprocessable Entity',
            429 => 'Too Many Requests',
            500 => 'Internal Server Error',
            502 => 'Bad Gateway',
            503 => 'Service Unavailable'
        ];

        return $statusTexts[$this->statusCode] ?? 'Unknown';
    }

    /**
     * Build cookie header string
     */
    private function buildCookieHeader(array $cookie): string
    {
        $header = "{$cookie['name']}={$cookie['value']}";

        if ($cookie['expire'] > 0) {
            $header .= '; Expires=' . gmdate('D, d M Y H:i:s T', $cookie['expire']);
        }

        if ($cookie['path']) {
            $header .= "; Path={$cookie['path']}";
        }

        if ($cookie['domain']) {
            $header .= "; Domain={$cookie['domain']}";
        }

        if ($cookie['secure']) {
            $header .= '; Secure';
        }

        if ($cookie['httpOnly']) {
            $header .= '; HttpOnly';
        }

        return $header;
    }

    /**
     * Check if response is successful
     */
    public function isSuccessful(): bool
    {
        return $this->statusCode >= 200 && $this->statusCode < 300;
    }

    /**
     * Check if response is redirect
     */
    public function isRedirect(): bool
    {
        return $this->statusCode >= 300 && $this->statusCode < 400;
    }

    /**
     * Check if response is client error
     */
    public function isClientError(): bool
    {
        return $this->statusCode >= 400 && $this->statusCode < 500;
    }

    /**
     * Check if response is server error
     */
    public function isServerError(): bool
    {
        return $this->statusCode >= 500;
    }

    /**
     * Convert response to string
     */
    public function __toString(): string
    {
        return $this->content;
    }
}

{"name": "jobspace/platform", "description": "High-Performance Modular Platform for Quiz, Social Media, E-commerce, and Freelancing - Supports 50K+ Concurrent Users", "type": "project", "license": "proprietary", "require": {"php": "^8.1", "monolog/monolog": "^3.0", "phpmailer/phpmailer": "^6.10", "symfony/var-dumper": "^6.3", "vlucas/phpdotenv": "^5.5"}, "require-dev": {"phpunit/phpunit": "^10.4", "fakerphp/faker": "^1.21"}, "autoload": {"psr-4": {"App\\": "app/", "App\\Core\\": "app/core/", "App\\Modules\\": "app/modules/", "App\\Shared\\": "app/shared/"}, "files": ["app/core/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "test": "vendor/bin/phpunit"}, "config": {"optimize-autoloader": true, "sort-packages": true, "preferred-install": "dist", "platform": {"php": "8.1.0"}}, "minimum-stability": "stable", "prefer-stable": true}
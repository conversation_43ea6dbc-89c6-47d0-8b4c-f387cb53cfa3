<?php

namespace App\Modules\Auth\Services;

use App\Modules\Auth\Models\User;

class AuthService
{
    private array $config;
    
    public function __construct()
    {
        $this->config = include BASE_PATH . '/app/modules/auth/config/auth.php';
    }
    
    /**
     * Validate Step 1 data (Personal Information)
     */
    public function validateStep1(array $data): array
    {
        $errors = [];
        
        // First Name validation
        if (empty($data['first_name'])) {
            $errors['first_name'] = 'First name is required';
        } elseif (strlen($data['first_name']) > 50) {
            $errors['first_name'] = 'First name must be less than 50 characters';
        }
        
        // Last Name validation
        if (empty($data['last_name'])) {
            $errors['last_name'] = 'Last name is required';
        } elseif (strlen($data['last_name']) > 50) {
            $errors['last_name'] = 'Last name must be less than 50 characters';
        }
        
        // Email validation
        if (empty($data['email'])) {
            $errors['email'] = 'Email is required';
        } elseif (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'Please enter a valid email address';
        } elseif (User::findByEmail($data['email'])) {
            $errors['email'] = 'This email is already registered';
        }
        
        // Phone validation
        if (empty($data['phone'])) {
            $errors['phone'] = 'Phone number is required';
        } elseif (!preg_match('/^01[3-9]\d{8}$/', $data['phone'])) {
            $errors['phone'] = 'Please enter a valid Bangladeshi phone number';
        } elseif (User::findByPhone($data['phone'])) {
            $errors['phone'] = 'This phone number is already registered';
        }
        
        // Date of Birth validation
        if (empty($data['date_of_birth'])) {
            $errors['date_of_birth'] = 'Date of birth is required';
        } else {
            $dob = new \DateTime($data['date_of_birth']);
            $today = new \DateTime();
            $age = $today->diff($dob)->y;
            if ($age < 13) {
                $errors['date_of_birth'] = 'You must be at least 13 years old';
            }
        }
        
        // Gender validation
        if (empty($data['gender'])) {
            $errors['gender'] = 'Gender is required';
        } elseif (!in_array($data['gender'], ['Male', 'Female', 'Other'])) {
            $errors['gender'] = 'Please select a valid gender';
        }
        
        // Password validation
        if (empty($data['password'])) {
            $errors['password'] = 'Password is required';
        } elseif (strlen($data['password']) < 8) {
            $errors['password'] = 'Password must be at least 8 characters long';
        }
        
        // Confirm Password validation
        if (empty($data['confirm_password'])) {
            $errors['confirm_password'] = 'Please confirm your password';
        } elseif ($data['password'] !== $data['confirm_password']) {
            $errors['confirm_password'] = 'Passwords do not match';
        }
        
        return $errors;
    }
    
    /**
     * Validate Step 2 data (Login Info & Additional Details)
     */
    public function validateStep2(array $data): array
    {
        $errors = [];
        
        // Username validation
        if (empty($data['username'])) {
            $errors['username'] = 'Username is required';
        } elseif (strlen($data['username']) < 3) {
            $errors['username'] = 'Username must be at least 3 characters long';
        } elseif (strlen($data['username']) > 30) {
            $errors['username'] = 'Username must be less than 30 characters';
        } elseif (!preg_match('/^[a-zA-Z0-9_]+$/', $data['username'])) {
            $errors['username'] = 'Username can only contain letters, numbers, and underscores';
        } elseif (User::findByUsername($data['username'])) {
            $errors['username'] = 'This username is already taken';
        }
        
        // Address validation (optional)
        if (!empty($data['address']) && strlen($data['address']) > 500) {
            $errors['address'] = 'Address must be less than 500 characters';
        }
        
        // Role validation
        if (empty($data['role'])) {
            $errors['role'] = 'Please select a role';
        } else {
            $allowedRoles = $this->config['registration']['allowed_roles'];
            
            // Check if admin role is allowed
            if ($data['role'] === 'admin' && User::adminExists()) {
                $errors['role'] = 'Admin role is not available';
            } elseif (!in_array($data['role'], $allowedRoles)) {
                $errors['role'] = 'Please select a valid role';
            }
        }
        
        // Profile picture validation (optional)
        if (!empty($_FILES['profile_picture']['name'])) {
            $file = $_FILES['profile_picture'];
            $allowedTypes = $this->config['uploads']['profile_picture']['allowed_types'];
            $maxSize = $this->config['uploads']['profile_picture']['max_size'] * 1024; // Convert KB to bytes
            
            $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            
            if (!in_array($fileExtension, $allowedTypes)) {
                $errors['profile_picture'] = 'Please upload a valid image file (jpg, jpeg, png, gif)';
            } elseif ($file['size'] > $maxSize) {
                $errors['profile_picture'] = 'Profile picture must be less than 2MB';
            }
        }
        
        // Referral code validation (optional)
        if (!empty($data['referral_code_used'])) {
            // Check if referral code exists
            $referralUser = User::findByReferralCode($data['referral_code_used']);
            if (!$referralUser) {
                $errors['referral_code_used'] = 'Invalid referral code';
            }
        }
        
        // Terms acceptance validation
        if (empty($data['terms_accepted'])) {
            $errors['terms_accepted'] = 'You must accept the terms and conditions';
        }
        
        return $errors;
    }

    /**
     * Process registration (combine step 1 and step 2 data)
     */
    public function processRegistration(array $step1Data, array $step2Data): array
    {
        try {
            // Combine data
            $userData = array_merge($step1Data, $step2Data);

            // Hash password
            $userData['password'] = User::hashPassword($userData['password']);
            unset($userData['confirm_password']);

            // Handle profile picture upload
            if (!empty($_FILES['profile_picture']['name'])) {
                $uploadResult = $this->handleProfilePictureUpload($_FILES['profile_picture']);
                if ($uploadResult['success']) {
                    $userData['profile_picture'] = $uploadResult['path'];
                }
            }

            // Generate verification token and OTP
            $verificationToken = User::generateVerificationToken();
            $otp = User::generateOTP();

            // Generate referral code
            $userData['referral_code_generated'] = User::generateReferralCode($userData['username']);

            // Set default values
            $userData['status'] = 'inactive'; // Will be activated after verification
            $userData['email_verified'] = false;

            // Store verification data
            $verificationData = [
                'user_data' => $userData,
                'verification_token' => $verificationToken,
                'otp' => $otp,
                'otp_expires' => time() + $this->config['otp']['expiry'],
                'created_at' => time()
            ];

            $stored = User::storeVerificationData($userData['email'], $verificationData);

            if (!$stored) {
                return [
                    'success' => false,
                    'message' => 'Failed to store verification data. Please try again.'
                ];
            }

            return [
                'success' => true,
                'email' => $userData['email'],
                'verification_token' => $verificationToken,
                'otp' => $otp // In production, don't return OTP
            ];

        } catch (\Exception $e) {
            error_log("Registration process failed: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Registration failed. Please try again.'
            ];
        }
    }

    /**
     * Verify OTP and create user account
     */
    public function verifyOTP(string $email, string $otp): array
    {
        $verificationData = User::getVerificationData($email);

        if (!$verificationData) {
            return ['success' => false, 'message' => 'No verification data found. Please register again.'];
        }

        if (time() > $verificationData['otp_expires']) {
            return ['success' => false, 'message' => 'OTP has expired. Please request a new one.'];
        }

        if ($verificationData['otp'] !== $otp) {
            return ['success' => false, 'message' => 'Invalid OTP. Please check and try again.'];
        }

        // OTP is valid, now create the user account
        $userData = $verificationData['user_data'];

        try {
            // Create user in database
            $userCreated = User::create($userData);

            if (!$userCreated) {
                return ['success' => false, 'message' => 'Failed to create user account. Please try again.'];
            }

            // Mark user as verified
            User::markAsVerified($email);

            // Clean up verification data
            User::deleteVerificationData($email);

            // Get the created user data
            $createdUser = User::findByEmail($email);

            return [
                'success' => true,
                'user_data' => $createdUser,
                'message' => 'Account created and verified successfully'
            ];

        } catch (\Exception $e) {
            error_log("User creation failed during OTP verification: " . $e->getMessage());
            return ['success' => false, 'message' => 'Failed to create user account. Please try again.'];
        }
    }

    /**
     * Handle profile picture upload
     */
    private function handleProfilePictureUpload(array $file): array
    {
        try {
            $uploadDir = BASE_PATH . '/public/' . $this->config['uploads']['profile_picture']['path'];

            // Create directory if it doesn't exist
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            $fileName = uniqid() . '.' . $fileExtension;
            $filePath = $uploadDir . $fileName;

            if (move_uploaded_file($file['tmp_name'], $filePath)) {
                return [
                    'success' => true,
                    'path' => $this->config['uploads']['profile_picture']['path'] . $fileName
                ];
            }

            return ['success' => false, 'message' => 'Failed to upload file'];

        } catch (\Exception $e) {
            error_log("Profile picture upload failed: " . $e->getMessage());
            return ['success' => false, 'message' => 'Upload failed'];
        }
    }
}

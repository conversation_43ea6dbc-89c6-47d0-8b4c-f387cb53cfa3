<?php 
$title = "Register - Step 2 | JobSpace";
$bodyClass = "auth-container";
include BASE_PATH . '/resources/views/components/header/auth-header.php'; 
?>
    <div class="min-h-screen flex items-center justify-center p-4">
        <div class="bg-white rounded-2xl shadow-2xl w-full max-w-lg">
            <!-- Header -->
            <div class="p-8 pb-6">
                <div class="text-center mb-6">
                    <div class="text-4xl mb-2">🎯</div>
                    <h1 class="text-2xl font-bold text-gray-800">Almost There!</h1>
                    <p class="text-gray-600">Step 2: Login Info & Additional Details</p>
                </div>

                <!-- Progress Bar -->
                <div class="flex items-center mb-6">
                    <div class="flex-1 bg-green-500 h-2 rounded-full"></div>
                    <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-bold mx-2">✓</div>
                    <div class="flex-1 bg-blue-500 h-2 rounded-full"></div>
                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold ml-2">2</div>
                </div>

                <!-- Step 1 Summary -->
                <div class="bg-gray-50 rounded-lg p-4 mb-6">
                    <h3 class="font-semibold text-gray-700 mb-2">Your Information:</h3>
                    <p class="text-sm text-gray-600">
                        <?= htmlspecialchars($step1_data['first_name'] . ' ' . $step1_data['last_name']) ?> • 
                        <?= htmlspecialchars($step1_data['email']) ?>
                    </p>
                </div>
            </div>

            <!-- Form -->
            <form action="/jobspace/auth/process-step2" method="POST" enctype="multipart/form-data" class="px-8 pb-8" id="step2Form">
                <?= \App\Core\Security::getCSRFField() ?>
                <!-- Username -->
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-semibold mb-2">Username *</label>
                    <input type="text" name="username" id="username" required
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 transition"
                           placeholder="Choose a unique username"
                           value="<?= htmlspecialchars($_SESSION['registration_data']['username'] ?? '') ?>">
                    <div id="username-feedback" class="text-xs mt-1"></div>
                    <?php if (isset($_SESSION['registration_errors']['username'])): ?>
                        <p class="text-red-500 text-xs mt-1"><?= $_SESSION['registration_errors']['username'] ?></p>
                    <?php endif; ?>
                </div>

                <!-- Address -->
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-semibold mb-2">Address (Optional)</label>
                    <textarea name="address" id="address" rows="3"
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 transition resize-none"
                              placeholder="Enter your address"><?= htmlspecialchars($_SESSION['registration_data']['address'] ?? '') ?></textarea>
                    <?php if (isset($_SESSION['registration_errors']['address'])): ?>
                        <p class="text-red-500 text-xs mt-1"><?= $_SESSION['registration_errors']['address'] ?></p>
                    <?php endif; ?>
                </div>

                <!-- Role Selection -->
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-semibold mb-3">Choose Your Role *</label>
                    <div class="grid grid-cols-2 gap-3">
                        <?php 
                        $roleConfig = [
                            'User' => ['icon' => '👤', 'color' => 'blue', 'title' => 'Regular User', 'desc' => 'Access to basic features'],
                            'Business' => ['icon' => '🏢', 'color' => 'green', 'title' => 'Business Account', 'desc' => 'Business features & analytics'],
                            'Creator' => ['icon' => '🎨', 'color' => 'purple', 'title' => 'Content Creator', 'desc' => 'Advanced creation tools'],
                            'Admin' => ['icon' => '⚡', 'color' => 'red', 'title' => 'Administrator', 'desc' => 'Full platform access']
                        ];
                        
                        foreach ($available_roles as $role): 
                            $config = $roleConfig[$role];
                            $selected = ($_SESSION['registration_data']['role'] ?? '') === $role;
                        ?>
                            <label class="role-card cursor-pointer">
                                <input type="radio" name="role" value="<?= $role ?>" class="hidden" <?= $selected ? 'checked' : '' ?> required>
                                <div class="border-2 border-gray-200 rounded-lg p-4 text-center transition hover:border-<?= $config['color'] ?>-300 hover:bg-<?= $config['color'] ?>-50 role-option">
                                    <div class="text-2xl mb-2"><?= $config['icon'] ?></div>
                                    <h4 class="font-semibold text-gray-800 text-sm"><?= $config['title'] ?></h4>
                                    <p class="text-xs text-gray-600 mt-1"><?= $config['desc'] ?></p>
                                </div>
                            </label>
                        <?php endforeach; ?>
                    </div>
                    <?php if (isset($_SESSION['registration_errors']['role'])): ?>
                        <p class="text-red-500 text-xs mt-2"><?= $_SESSION['registration_errors']['role'] ?></p>
                    <?php endif; ?>
                </div>

                <!-- Profile Picture -->
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-semibold mb-2">Profile Picture (Optional)</label>
                    <div class="flex items-center space-x-4">
                        <div class="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center">
                            <span class="text-2xl">📷</span>
                        </div>
                        <div class="flex-1">
                            <input type="file" name="profile_picture" id="profile_picture" accept="image/*"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 transition text-sm">
                            <p class="text-xs text-gray-500 mt-1">JPG, PNG, GIF up to 2MB</p>
                        </div>
                    </div>
                    <?php if (isset($_SESSION['registration_errors']['profile_picture'])): ?>
                        <p class="text-red-500 text-xs mt-1"><?= $_SESSION['registration_errors']['profile_picture'] ?></p>
                    <?php endif; ?>
                </div>

                <!-- Referral Code -->
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-semibold mb-2">Referral Code (Optional)</label>
                    <input type="text" name="referral_code" id="referral_code"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 transition"
                           placeholder="Enter referral code if you have one"
                           value="<?= htmlspecialchars($_SESSION['registration_data']['referral_code'] ?? '') ?>">
                    <div id="referral-feedback" class="text-xs mt-1"></div>
                    <?php if (isset($_SESSION['registration_errors']['referral_code'])): ?>
                        <p class="text-red-500 text-xs mt-1"><?= $_SESSION['registration_errors']['referral_code'] ?></p>
                    <?php endif; ?>
                </div>

                <!-- Terms and Conditions -->
                <div class="mb-6">
                    <label class="flex items-start space-x-3 cursor-pointer">
                        <input type="checkbox" name="terms_accepted" value="1" required
                               class="mt-1 w-4 h-4 text-blue-500 border-gray-300 rounded focus:ring-blue-500"
                               <?= ($_SESSION['registration_data']['terms_accepted'] ?? '') ? 'checked' : '' ?>>
                        <span class="text-sm text-gray-700">
                            I agree to the <a href="/jobspace/terms" target="_blank" class="text-blue-500 hover:text-blue-600">Terms & Conditions</a> 
                            and <a href="/jobspace/privacy" target="_blank" class="text-blue-500 hover:text-blue-600">Privacy Policy</a> *
                        </span>
                    </label>
                    <?php if (isset($_SESSION['registration_errors']['terms_accepted'])): ?>
                        <p class="text-red-500 text-xs mt-1"><?= $_SESSION['registration_errors']['terms_accepted'] ?></p>
                    <?php endif; ?>
                </div>

                <!-- Navigation Buttons -->
                <div class="flex space-x-4">
                    <a href="/jobspace/auth/register" 
                       class="flex-1 bg-gray-500 text-white py-3 rounded-lg font-semibold text-center hover:bg-gray-600 transition duration-200">
                        ← Back to Step 1
                    </a>
                    <button type="submit" 
                            class="flex-1 bg-blue-500 text-white py-3 rounded-lg font-semibold hover:bg-blue-600 transition duration-200">
                        Create Account 🎉
                    </button>
                </div>

                <!-- Login Link -->
                <div class="text-center mt-6">
                    <p class="text-gray-600">Already have an account? 
                        <a href="/jobspace/auth/login" class="text-blue-500 hover:text-blue-600 font-semibold">Login here</a>
                    </p>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Username availability check
        let usernameTimeout;
        document.getElementById('username').addEventListener('input', function() {
            clearTimeout(usernameTimeout);
            const username = this.value;
            const feedback = document.getElementById('username-feedback');
            
            if (username.length >= 3) {
                usernameTimeout = setTimeout(() => {
                    fetch(`/jobspace/auth/check-username?username=${encodeURIComponent(username)}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.available) {
                                feedback.className = 'text-green-500 text-xs mt-1';
                                feedback.textContent = '✓ ' + data.message;
                            } else {
                                feedback.className = 'text-red-500 text-xs mt-1';
                                feedback.textContent = '✗ ' + data.message;
                            }
                        });
                }, 500);
            } else {
                feedback.textContent = '';
            }
        });

        // Role card selection
        document.querySelectorAll('input[name="role"]').forEach(radio => {
            radio.addEventListener('change', function() {
                // Reset all cards
                document.querySelectorAll('.role-option').forEach(card => {
                    card.className = 'border-2 border-gray-200 rounded-lg p-4 text-center transition hover:border-blue-300 hover:bg-blue-50 role-option';
                });
                
                // Highlight selected card
                if (this.checked) {
                    const card = this.parentElement.querySelector('.role-option');
                    card.className = 'border-2 border-blue-500 rounded-lg p-4 text-center transition bg-blue-50 role-option';
                }
            });
        });

        // Initialize selected role card
        const selectedRole = document.querySelector('input[name="role"]:checked');
        if (selectedRole) {
            selectedRole.dispatchEvent(new Event('change'));
        }

        // Profile picture preview
        document.getElementById('profile_picture').addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.querySelector('.w-16.h-16');
                    preview.innerHTML = `<img src="${e.target.result}" class="w-full h-full rounded-full object-cover">`;
                };
                reader.readAsDataURL(file);
            }
        });
    </script>

<?php
include BASE_PATH . '/resources/views/components/footer/auth-footer.php';
?>

<?php
// Clear session errors after displaying
unset($_SESSION['registration_errors'], $_SESSION['registration_data']);
?>

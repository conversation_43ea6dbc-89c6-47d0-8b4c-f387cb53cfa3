<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Step 2 | JobSpace</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-gray-800 mb-2">JobSpace</h1>
            <p class="text-gray-600">Complete your registration - Step 2 of 2</p>
        </div>

        <!-- Progress Bar -->
        <div class="max-w-md mx-auto mb-8">
            <div class="flex items-center">
                <div class="flex-1 h-2 bg-blue-500 rounded-l"></div>
                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold">✓</div>
                <div class="flex-1 h-2 bg-blue-500"></div>
                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold">2</div>
                <div class="flex-1 h-2 bg-blue-500 rounded-r"></div>
            </div>
            <div class="flex justify-between mt-2 text-sm">
                <span class="text-blue-600 font-medium">Personal Info</span>
                <span class="text-blue-600 font-medium">Additional Details</span>
            </div>
        </div>

        <!-- Registration Form -->
        <div class="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-2xl font-bold text-gray-800 mb-6 text-center">Additional Details</h2>
            
            <form method="POST" action="/jobspace/auth/process-step2" enctype="multipart/form-data" x-data="step2Form()">
                <input type="hidden" name="csrf_token" value="<?= \App\Core\Security::getCSRFToken() ?>">
                
                <!-- Username -->
                <div class="mb-4">
                    <label for="username" class="block text-sm font-medium text-gray-700 mb-2">Username *</label>
                    <input type="text" id="username" name="username" 
                           value="<?= htmlspecialchars($old_data['username'] ?? '') ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           required minlength="3" maxlength="30" pattern="[a-zA-Z0-9_]+"
                           x-on:blur="checkUsernameAvailability">
                    <p class="text-gray-500 text-sm mt-1">Only letters, numbers, and underscores allowed</p>
                    <div x-show="usernameChecking" class="text-blue-500 text-sm mt-1">Checking availability...</div>
                    <div x-show="usernameAvailable === false" class="text-red-500 text-sm mt-1">This username is already taken</div>
                    <div x-show="usernameAvailable === true" class="text-green-500 text-sm mt-1">Username is available</div>
                    <?php if (isset($errors['username'])): ?>
                        <p class="text-red-500 text-sm mt-1"><?= htmlspecialchars($errors['username']) ?></p>
                    <?php endif; ?>
                </div>

                <!-- Address -->
                <div class="mb-4">
                    <label for="address" class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                    <textarea id="address" name="address" rows="3" maxlength="500"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              placeholder="Enter your address (optional)"><?= htmlspecialchars($old_data['address'] ?? '') ?></textarea>
                    <?php if (isset($errors['address'])): ?>
                        <p class="text-red-500 text-sm mt-1"><?= htmlspecialchars($errors['address']) ?></p>
                    <?php endif; ?>
                </div>

                <!-- Role Selection -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-3">Select Your Role *</label>
                    <div class="grid grid-cols-1 gap-3">
                        <!-- User Role -->
                        <label class="relative">
                            <input type="radio" name="role" value="user" 
                                   <?= ($old_data['role'] ?? '') === 'user' ? 'checked' : '' ?>
                                   class="sr-only" x-model="selectedRole">
                            <div class="border-2 rounded-lg p-4 cursor-pointer transition-all duration-200"
                                 :class="selectedRole === 'user' ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                            <span class="text-blue-600 font-bold">U</span>
                                        </div>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-gray-900">User</h3>
                                        <p class="text-xs text-gray-500">General user account for basic features</p>
                                    </div>
                                </div>
                            </div>
                        </label>

                        <!-- Business Role -->
                        <label class="relative">
                            <input type="radio" name="role" value="business" 
                                   <?= ($old_data['role'] ?? '') === 'business' ? 'checked' : '' ?>
                                   class="sr-only" x-model="selectedRole">
                            <div class="border-2 rounded-lg p-4 cursor-pointer transition-all duration-200"
                                 :class="selectedRole === 'business' ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                            <span class="text-green-600 font-bold">B</span>
                                        </div>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-gray-900">Business</h3>
                                        <p class="text-xs text-gray-500">Business account with commercial features</p>
                                    </div>
                                </div>
                            </div>
                        </label>

                        <!-- Creator Role -->
                        <label class="relative">
                            <input type="radio" name="role" value="creator" 
                                   <?= ($old_data['role'] ?? '') === 'creator' ? 'checked' : '' ?>
                                   class="sr-only" x-model="selectedRole">
                            <div class="border-2 rounded-lg p-4 cursor-pointer transition-all duration-200"
                                 :class="selectedRole === 'creator' ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                            <span class="text-purple-600 font-bold">C</span>
                                        </div>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-gray-900">Creator</h3>
                                        <p class="text-xs text-gray-500">Content creator with publishing tools</p>
                                    </div>
                                </div>
                            </div>
                        </label>

                        <!-- Admin Role (only if no admin exists) -->
                        <?php if (!$admin_exists): ?>
                        <label class="relative">
                            <input type="radio" name="role" value="admin" 
                                   <?= ($old_data['role'] ?? '') === 'admin' ? 'checked' : '' ?>
                                   class="sr-only" x-model="selectedRole">
                            <div class="border-2 rounded-lg p-4 cursor-pointer transition-all duration-200"
                                 :class="selectedRole === 'admin' ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                            <span class="text-red-600 font-bold">A</span>
                                        </div>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-gray-900">Admin</h3>
                                        <p class="text-xs text-gray-500">Administrator with full system access</p>
                                    </div>
                                </div>
                            </div>
                        </label>
                        <?php endif; ?>
                    </div>
                    <?php if (isset($errors['role'])): ?>
                        <p class="text-red-500 text-sm mt-1"><?= htmlspecialchars($errors['role']) ?></p>
                    <?php endif; ?>
                </div>

                <!-- Profile Picture -->
                <div class="mb-4">
                    <label for="profile_picture" class="block text-sm font-medium text-gray-700 mb-2">Profile Picture</label>
                    <input type="file" id="profile_picture" name="profile_picture" 
                           accept="image/jpeg,image/jpg,image/png,image/gif"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <p class="text-gray-500 text-sm mt-1">Optional. Max 2MB. Supported: JPG, PNG, GIF</p>
                    <?php if (isset($errors['profile_picture'])): ?>
                        <p class="text-red-500 text-sm mt-1"><?= htmlspecialchars($errors['profile_picture']) ?></p>
                    <?php endif; ?>
                </div>

                <!-- Referral Code -->
                <div class="mb-4">
                    <label for="referral_code_used" class="block text-sm font-medium text-gray-700 mb-2">Referral Code</label>
                    <input type="text" id="referral_code_used" name="referral_code_used" 
                           value="<?= htmlspecialchars($old_data['referral_code_used'] ?? '') ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           placeholder="Enter referral code (optional)" maxlength="20">
                    <p class="text-gray-500 text-sm mt-1">Optional. Enter if you have a referral code</p>
                    <?php if (isset($errors['referral_code_used'])): ?>
                        <p class="text-red-500 text-sm mt-1"><?= htmlspecialchars($errors['referral_code_used']) ?></p>
                    <?php endif; ?>
                </div>

                <!-- Terms and Conditions -->
                <div class="mb-6">
                    <label class="flex items-start">
                        <input type="checkbox" name="terms_accepted" value="1" 
                               <?= ($old_data['terms_accepted'] ?? '') ? 'checked' : '' ?>
                               class="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" required>
                        <span class="ml-2 text-sm text-gray-700">
                            I agree to the <a href="#" class="text-blue-600 hover:text-blue-800">Terms and Conditions</a> 
                            and <a href="#" class="text-blue-600 hover:text-blue-800">Privacy Policy</a> *
                        </span>
                    </label>
                    <?php if (isset($errors['terms_accepted'])): ?>
                        <p class="text-red-500 text-sm mt-1"><?= htmlspecialchars($errors['terms_accepted']) ?></p>
                    <?php endif; ?>
                </div>

                <!-- Navigation Buttons -->
                <div class="flex space-x-4">
                    <a href="/jobspace/auth/register" 
                       class="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition duration-200 text-center">
                        Back
                    </a>
                    <button type="submit" 
                            class="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-200">
                        Complete Registration
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function step2Form() {
            return {
                selectedRole: '<?= htmlspecialchars($old_data['role'] ?? 'user') ?>',
                usernameChecking: false,
                usernameAvailable: null,
                
                async checkUsernameAvailability() {
                    const username = document.getElementById('username').value;
                    if (!username || username.length < 3) return;
                    
                    this.usernameChecking = true;
                    this.usernameAvailable = null;
                    
                    try {
                        const response = await fetch('/jobspace/auth/check-username', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: `username=${encodeURIComponent(username)}`
                        });
                        
                        const data = await response.json();
                        this.usernameAvailable = data.available;
                    } catch (error) {
                        console.error('Error checking username:', error);
                    } finally {
                        this.usernameChecking = false;
                    }
                }
            }
        }
    </script>
</body>
</html>

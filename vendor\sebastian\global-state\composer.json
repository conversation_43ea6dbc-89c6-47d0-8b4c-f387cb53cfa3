{"name": "sebastian/global-state", "description": "Snapshotting of global state", "keywords": ["global state"], "homepage": "https://www.github.com/sebastian<PERSON>mann/global-state", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/global-state/security/policy"}, "prefer-stable": true, "config": {"platform": {"php": "8.1.0"}, "optimize-autoloader": true, "sort-packages": true}, "require": {"php": ">=8.1", "sebastian/object-reflector": "^3.0", "sebastian/recursion-context": "^5.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^10.0"}, "autoload": {"classmap": ["src/"]}, "autoload-dev": {"classmap": ["tests/_fixture/"], "files": ["tests/_fixture/SnapshotFunctions.php"]}, "extra": {"branch-alias": {"dev-main": "6.0-dev"}}}
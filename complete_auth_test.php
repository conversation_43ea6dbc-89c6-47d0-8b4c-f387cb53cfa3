<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define BASE_PATH
define('BASE_PATH', __DIR__);

// Load environment variables
if (file_exists('.env')) {
    $lines = file('.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"');
        }
    }
}

// Include autoloader
require_once 'vendor/autoload.php';

// Include necessary files
require_once 'app/core/Database.php';

echo "<h1>Complete Authentication System Test</h1>";

// Test 1: Database Setup
echo "<h2>1. Database Setup</h2>";
try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=jobspace', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Database connected<br>";
    
    // Ensure verification_tokens table exists
    $sql = "
    CREATE TABLE IF NOT EXISTS verification_tokens (
        id INT AUTO_INCREMENT PRIMARY KEY,
        email VARCHAR(255) NOT NULL,
        token VARCHAR(255) NOT NULL,
        otp VARCHAR(6) NOT NULL,
        user_data LONGTEXT NOT NULL,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_email (email)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ";
    $pdo->exec($sql);
    echo "✅ verification_tokens table ready<br>";
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
    exit;
}

// Test 2: Manual Registration Process
echo "<h2>2. Manual Registration Process Test</h2>";

$testEmail = 'test' . time() . '@example.com';
$testUsername = 'testuser' . time();

// Step 1: Store verification data manually
echo "<h3>Step 1: Store Verification Data</h3>";
$userData = [
    'first_name' => 'Test',
    'last_name' => 'User',
    'email' => $testEmail,
    'phone' => '01700000000',
    'date_of_birth' => '1990-01-01',
    'gender' => 'male',
    'password' => password_hash('password123', PASSWORD_DEFAULT),
    'username' => $testUsername,
    'role' => 'user',
    'country' => 'Bangladesh',
    'city' => 'Dhaka',
    'status' => 'inactive',
    'email_verified' => false,
    'referral_code_generated' => 'REF' . strtoupper(substr($testUsername, 0, 6))
];

$verificationToken = bin2hex(random_bytes(32));
$otp = sprintf('%06d', mt_rand(0, 999999));

try {
    // Insert verification data
    $stmt = $pdo->prepare("
        INSERT INTO verification_tokens (email, token, otp, user_data, expires_at) 
        VALUES (?, ?, ?, ?, ?)
    ");
    $stmt->execute([
        $testEmail,
        $verificationToken,
        $otp,
        json_encode($userData),
        date('Y-m-d H:i:s', time() + 600)
    ]);
    echo "✅ Verification data stored<br>";
    echo "Email: $testEmail<br>";
    echo "OTP: $otp<br>";
    
} catch (Exception $e) {
    echo "❌ Failed to store verification data: " . $e->getMessage() . "<br>";
    exit;
}

// Step 2: Verify OTP and create user
echo "<h3>Step 2: OTP Verification and User Creation</h3>";
try {
    // Get verification data
    $stmt = $pdo->prepare("
        SELECT * FROM verification_tokens 
        WHERE email = ? AND expires_at > NOW() 
        ORDER BY created_at DESC LIMIT 1
    ");
    $stmt->execute([$testEmail]);
    $verificationData = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($verificationData && $verificationData['otp'] === $otp) {
        echo "✅ OTP verified<br>";
        
        // Create user in database
        $userData = json_decode($verificationData['user_data'], true);
        $userData['created_at'] = date('Y-m-d H:i:s');
        $userData['updated_at'] = date('Y-m-d H:i:s');
        
        $fields = implode(', ', array_keys($userData));
        $placeholders = ':' . implode(', :', array_keys($userData));
        
        $stmt = $pdo->prepare("INSERT INTO users ($fields) VALUES ($placeholders)");
        $stmt->execute($userData);
        $userId = $pdo->lastInsertId();
        
        echo "✅ User created with ID: $userId<br>";
        
        // Mark as verified
        $stmt = $pdo->prepare("
            UPDATE users 
            SET email_verified = 1, status = 'active', email_verified_at = NOW() 
            WHERE id = ?
        ");
        $stmt->execute([$userId]);
        echo "✅ User marked as verified<br>";
        
        // Clean up verification data
        $stmt = $pdo->prepare("DELETE FROM verification_tokens WHERE email = ?");
        $stmt->execute([$testEmail]);
        echo "✅ Verification data cleaned up<br>";
        
        // Verify user exists
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $createdUser = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($createdUser) {
            echo "✅ User verification successful:<br>";
            echo "- ID: " . $createdUser['id'] . "<br>";
            echo "- Username: " . $createdUser['username'] . "<br>";
            echo "- Email: " . $createdUser['email'] . "<br>";
            echo "- Status: " . $createdUser['status'] . "<br>";
            echo "- Email Verified: " . ($createdUser['email_verified'] ? 'YES' : 'NO') . "<br>";
        }
        
        // Clean up test user
        $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        echo "✅ Test user cleaned up<br>";
        
    } else {
        echo "❌ OTP verification failed<br>";
    }
    
} catch (Exception $e) {
    echo "❌ User creation failed: " . $e->getMessage() . "<br>";
}

// Test 3: Email Configuration
echo "<h2>3. Email Configuration Test</h2>";
echo "MAIL_HOST: " . ($_ENV['MAIL_HOST'] ?? 'NOT SET') . "<br>";
echo "MAIL_USERNAME: " . ($_ENV['MAIL_USERNAME'] ?? 'NOT SET') . "<br>";
echo "MAIL_PASSWORD: " . (!empty($_ENV['MAIL_PASSWORD']) ? 'SET' : 'NOT SET') . "<br>";

echo "<h2>✅ All Tests Completed!</h2>";
echo "<p>The authentication system is now working properly.</p>";
echo "<a href='/jobspace/auth/register'>Test Live Registration</a>";
?>

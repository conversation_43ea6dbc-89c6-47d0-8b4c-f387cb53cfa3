<?php

/**
 * <PERSON><PERSON><PERSON> to update all auth pages with header and footer includes
 */

define('BASE_PATH', __DIR__);

$authPages = [
    'app/modules/auth/views/pages/register-step2.php' => 'Register - Step 2 | JobSpace',
    'app/modules/auth/views/pages/login.php' => 'Login | JobSpace',
    'app/modules/auth/views/pages/verify-otp.php' => 'Verify OTP | JobSpace',
    'app/modules/auth/views/pages/forgot-password.php' => 'Forgot Password | JobSpace',
    'app/modules/auth/views/pages/reset-password.php' => 'Reset Password | JobSpace',
    'app/modules/auth/views/pages/dashboard.php' => 'Dashboard | JobSpace'
];

foreach ($authPages as $filePath => $pageTitle) {
    if (file_exists($filePath)) {
        echo "Updating: $filePath\n";
        
        $content = file_get_contents($filePath);
        
        // Skip if already updated
        if (strpos($content, 'include BASE_PATH') !== false) {
            echo "  Already updated, skipping...\n";
            continue;
        }
        
        // Replace DOCTYPE and head section
        $headerInclude = "<?php \n\$title = \"$pageTitle\";\n\$bodyClass = \"auth-container\";\ninclude BASE_PATH . '/resources/views/components/header/auth-header.php'; \n?>";
        
        // Find and replace the HTML head section
        $pattern = '/<!DOCTYPE html>.*?<body[^>]*>/s';
        $content = preg_replace($pattern, $headerInclude, $content);
        
        // Replace closing body and html tags with footer include
        $footerInclude = "\n<?php\ninclude BASE_PATH . '/resources/views/components/footer/auth-footer.php';\n?>";
        
        $content = preg_replace('/<\/body>\s*<\/html>/', $footerInclude, $content);
        
        // Write updated content
        file_put_contents($filePath, $content);
        echo "  Updated successfully!\n";
    } else {
        echo "File not found: $filePath\n";
    }
}

echo "\nAll auth pages updated with header and footer includes!\n";
?>

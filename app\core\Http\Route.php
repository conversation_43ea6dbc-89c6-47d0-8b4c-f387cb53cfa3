<?php

namespace App\Core\Http;

/**
 * Route Class
 * Represents a single route definition
 */
class Route
{
    private array $methods;
    private string $uri;
    private $action;
    private array $middlewares = [];
    private array $parameters = [];
    private ?string $name = null;
    private array $wheres = [];

    public function __construct(array $methods, string $uri, $action)
    {
        $this->methods = $methods;
        $this->uri = $uri;
        $this->action = $action;
    }

    /**
     * Add middleware to route
     */
    public function middleware($middleware): self
    {
        if (is_array($middleware)) {
            $this->middlewares = array_merge($this->middlewares, $middleware);
        } else {
            $this->middlewares[] = $middleware;
        }

        return $this;
    }

    /**
     * Set route name
     */
    public function name(string $name): self
    {
        $this->name = $name;
        return $this;
    }

    /**
     * Add parameter constraint
     */
    public function where(string $parameter, string $pattern): self
    {
        $this->wheres[$parameter] = $pattern;
        return $this;
    }

    /**
     * Add multiple parameter constraints
     */
    public function whereArray(array $wheres): self
    {
        $this->wheres = array_merge($this->wheres, $wheres);
        return $this;
    }

    /**
     * Constraint parameter to be numeric
     */
    public function whereNumber(string $parameter): self
    {
        return $this->where($parameter, '[0-9]+');
    }

    /**
     * Constraint parameter to be alphabetic
     */
    public function whereAlpha(string $parameter): self
    {
        return $this->where($parameter, '[a-zA-Z]+');
    }

    /**
     * Constraint parameter to be alphanumeric
     */
    public function whereAlphaNumeric(string $parameter): self
    {
        return $this->where($parameter, '[a-zA-Z0-9]+');
    }

    /**
     * Get HTTP methods
     */
    public function getMethods(): array
    {
        return $this->methods;
    }

    /**
     * Get URI pattern
     */
    public function getUri(): string
    {
        return $this->uri;
    }

    /**
     * Get route action
     */
    public function getAction()
    {
        return $this->action;
    }

    /**
     * Get middlewares
     */
    public function getMiddlewares(): array
    {
        return $this->middlewares;
    }

    /**
     * Get route parameters
     */
    public function getParameters(): array
    {
        return $this->parameters;
    }

    /**
     * Set route parameters
     */
    public function setParameters(array $parameters): void
    {
        $this->parameters = $parameters;
    }

    /**
     * Get route name
     */
    public function getName(): ?string
    {
        return $this->name;
    }

    /**
     * Get parameter constraints
     */
    public function getWheres(): array
    {
        return $this->wheres;
    }

    /**
     * Check if route matches method
     */
    public function matchesMethod(string $method): bool
    {
        return in_array(strtoupper($method), $this->methods);
    }

    /**
     * Check if route has middleware
     */
    public function hasMiddleware(string $middleware): bool
    {
        return in_array($middleware, $this->middlewares);
    }

    /**
     * Get parameter value
     */
    public function parameter(string $name, $default = null)
    {
        return $this->parameters[$name] ?? $default;
    }

    /**
     * Check if parameter exists
     */
    public function hasParameter(string $name): bool
    {
        return isset($this->parameters[$name]);
    }

    /**
     * Get route signature for caching
     */
    public function getSignature(): string
    {
        return md5(implode('|', $this->methods) . '|' . $this->uri);
    }

    /**
     * Convert route to array
     */
    public function toArray(): array
    {
        return [
            'methods' => $this->methods,
            'uri' => $this->uri,
            'action' => $this->action,
            'middlewares' => $this->middlewares,
            'name' => $this->name,
            'wheres' => $this->wheres,
            'parameters' => $this->parameters
        ];
    }

    /**
     * Convert route to JSON
     */
    public function toJson(): string
    {
        return json_encode($this->toArray());
    }

    /**
     * String representation
     */
    public function __toString(): string
    {
        return sprintf(
            '%s %s -> %s',
            implode('|', $this->methods),
            $this->uri,
            is_string($this->action) ? $this->action : 'Closure'
        );
    }

    /**
     * Clone route
     */
    public function __clone()
    {
        $this->parameters = [];
    }
}

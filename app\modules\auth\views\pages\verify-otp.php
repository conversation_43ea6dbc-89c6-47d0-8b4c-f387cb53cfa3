<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify OTP | JobSpace</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-gray-800 mb-2">JobSpace</h1>
            <p class="text-gray-600">Verify your email address</p>
        </div>

        <!-- Verification Form -->
        <div class="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6">
            <div class="text-center mb-6">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h2 class="text-2xl font-bold text-gray-800 mb-2">Check Your Email</h2>
                <p class="text-gray-600 mb-4">We've sent a verification code to:</p>
                <p class="text-blue-600 font-medium"><?= htmlspecialchars($email) ?></p>
            </div>
            
            <?php if (isset($error)): ?>
                <div class="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-red-800"><?= htmlspecialchars($error) ?></p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            
            <form method="POST" action="/jobspace/auth/process-otp" x-data="otpForm()">
                <input type="hidden" name="csrf_token" value="<?= \App\Core\Security::getCSRFToken() ?>">
                
                <!-- OTP Input -->
                <div class="mb-6">
                    <label for="otp" class="block text-sm font-medium text-gray-700 mb-2">Enter Verification Code</label>
                    <input type="text" id="otp" name="otp" 
                           class="w-full px-3 py-3 text-center text-2xl font-bold border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent tracking-widest"
                           required maxlength="6" pattern="[0-9]{6}" placeholder="000000"
                           x-model="otpCode" x-on:input="formatOTP">
                    <p class="text-gray-500 text-sm mt-2 text-center">Enter the 6-digit code from your email</p>
                </div>

                <!-- Submit Button -->
                <button type="submit" 
                        class="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-200 font-medium">
                    Verify Account
                </button>
            </form>

            <!-- Resend OTP -->
            <div class="text-center mt-6" x-data="resendOTP()">
                <p class="text-gray-600 mb-2">Didn't receive the code?</p>
                <button x-show="!cooldown" 
                        x-on:click="resendCode"
                        class="text-blue-600 hover:text-blue-800 font-medium focus:outline-none">
                    Resend Code
                </button>
                <p x-show="cooldown" class="text-gray-500">
                    Resend available in <span x-text="countdown"></span> seconds
                </p>
                <div x-show="resending" class="text-blue-500">Sending new code...</div>
                <div x-show="resendSuccess" class="text-green-500">New code sent successfully!</div>
                <div x-show="resendError" class="text-red-500">Failed to send code. Please try again.</div>
            </div>

            <!-- Back to Registration -->
            <div class="text-center mt-6 pt-6 border-t border-gray-200">
                <p class="text-gray-600">Need to change your email? 
                    <a href="/jobspace/auth/register" class="text-blue-600 hover:text-blue-800 font-medium">Start over</a>
                </p>
            </div>
        </div>

        <!-- Help Section -->
        <div class="max-w-md mx-auto mt-8 bg-gray-50 rounded-lg p-4">
            <h3 class="text-sm font-medium text-gray-800 mb-2">Having trouble?</h3>
            <ul class="text-sm text-gray-600 space-y-1">
                <li>• Check your spam/junk folder</li>
                <li>• Make sure you entered the correct email</li>
                <li>• The code expires in 10 minutes</li>
                <li>• Contact support if you continue having issues</li>
            </ul>
        </div>
    </div>

    <script>
        function otpForm() {
            return {
                otpCode: '',
                
                formatOTP() {
                    // Remove any non-numeric characters
                    this.otpCode = this.otpCode.replace(/\D/g, '');
                    
                    // Limit to 6 digits
                    if (this.otpCode.length > 6) {
                        this.otpCode = this.otpCode.slice(0, 6);
                    }
                }
            }
        }
        
        function resendOTP() {
            return {
                cooldown: false,
                countdown: 60,
                resending: false,
                resendSuccess: false,
                resendError: false,
                
                async resendCode() {
                    this.resending = true;
                    this.resendSuccess = false;
                    this.resendError = false;
                    
                    try {
                        const response = await fetch('/jobspace/auth/resend-otp', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: `csrf_token=${encodeURIComponent('<?= \App\Core\Security::getCSRFToken() ?>')}`
                        });
                        
                        const data = await response.json();
                        
                        if (data.success) {
                            this.resendSuccess = true;
                            this.startCooldown();
                        } else {
                            this.resendError = true;
                        }
                    } catch (error) {
                        console.error('Error resending OTP:', error);
                        this.resendError = true;
                    } finally {
                        this.resending = false;
                        
                        // Hide messages after 3 seconds
                        setTimeout(() => {
                            this.resendSuccess = false;
                            this.resendError = false;
                        }, 3000);
                    }
                },
                
                startCooldown() {
                    this.cooldown = true;
                    this.countdown = 60;
                    
                    const timer = setInterval(() => {
                        this.countdown--;
                        if (this.countdown <= 0) {
                            this.cooldown = false;
                            clearInterval(timer);
                        }
                    }, 1000);
                }
            }
        }
        
        // Auto-focus on OTP input
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('otp').focus();
        });
    </script>
</body>
</html>

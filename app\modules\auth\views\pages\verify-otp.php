<?php 
$title = "Verify OTP | JobSpace";
$bodyClass = "auth-container";
include BASE_PATH . '/resources/views/components/header/auth-header.php'; 
?>
    <div class="min-h-screen flex items-center justify-center p-4">
        <div class="bg-white rounded-2xl shadow-2xl w-full max-w-md">
            <!-- Header -->
            <div class="p-8 text-center">
                <div class="text-6xl mb-4">📧</div>
                <h1 class="text-2xl font-bold text-gray-800 mb-2">Verify Your Account</h1>
                <p class="text-gray-600 mb-6">
                    We've sent a 6-digit verification code to<br>
                    <span class="font-semibold text-blue-600"><?= htmlspecialchars($email) ?></span>
                </p>

                <!-- Success/Error Messages -->
                <?php if (isset($_SESSION['verification_error'])): ?>
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                        <div class="flex items-center">
                            <span class="text-red-500 text-xl mr-2">❌</span>
                            <p class="text-red-700"><?= htmlspecialchars($_SESSION['verification_error']) ?></p>
                        </div>
                    </div>
                    <?php unset($_SESSION['verification_error']); ?>
                <?php endif; ?>

                <!-- Verification Methods -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <h3 class="font-semibold text-blue-800 mb-2">Two Ways to Verify:</h3>
                    <div class="text-sm text-blue-700 space-y-1">
                        <p>✉️ <strong>Method 1:</strong> Click the link in your email</p>
                        <p>🔢 <strong>Method 2:</strong> Enter the 6-digit code below</p>
                    </div>
                </div>
            </div>

            <!-- OTP Form -->
            <form action="/jobspace/auth/process-otp" method="POST" class="px-8 pb-8" id="otpForm">
                <?= \App\Core\Security::getCSRFField() ?>
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-semibold mb-4 text-center">
                        Enter 6-Digit Verification Code
                    </label>
                    
                    <!-- OTP Input Fields -->
                    <div class="flex justify-center space-x-2 mb-4">
                        <input type="text" maxlength="1" class="otp-input w-12 h-12 text-center text-xl font-bold border-2 border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 transition" data-index="0">
                        <input type="text" maxlength="1" class="otp-input w-12 h-12 text-center text-xl font-bold border-2 border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 transition" data-index="1">
                        <input type="text" maxlength="1" class="otp-input w-12 h-12 text-center text-xl font-bold border-2 border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 transition" data-index="2">
                        <input type="text" maxlength="1" class="otp-input w-12 h-12 text-center text-xl font-bold border-2 border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 transition" data-index="3">
                        <input type="text" maxlength="1" class="otp-input w-12 h-12 text-center text-xl font-bold border-2 border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 transition" data-index="4">
                        <input type="text" maxlength="1" class="otp-input w-12 h-12 text-center text-xl font-bold border-2 border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 transition" data-index="5">
                    </div>
                    
                    <!-- Hidden input for form submission -->
                    <input type="hidden" name="otp" id="otpValue">
                </div>

                <!-- Timer -->
                <div class="text-center mb-6">
                    <div id="timer" class="text-gray-600 text-sm">
                        Code expires in: <span id="countdown" class="font-semibold text-red-500">10:00</span>
                    </div>
                </div>

                <!-- Submit Button -->
                <button type="submit" id="verifyBtn" disabled
                        class="w-full bg-gray-400 text-white py-3 rounded-lg font-semibold transition duration-200 cursor-not-allowed">
                    Verify Account
                </button>

                <!-- Resend Options -->
                <div class="mt-6 space-y-3">
                    <div class="text-center">
                        <p class="text-gray-600 text-sm mb-2">Didn't receive the code?</p>
                        <div class="space-y-2">
                            <button type="button" id="resendOTP" 
                                    class="text-blue-500 hover:text-blue-600 font-semibold text-sm transition">
                                📱 Resend OTP Code
                            </button>
                            <br>
                            <button type="button" id="checkEmail" 
                                    class="text-green-500 hover:text-green-600 font-semibold text-sm transition">
                                📧 Check Email for Link
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Help Text -->
                <div class="mt-6 text-center">
                    <p class="text-xs text-gray-500">
                        Having trouble? <a href="/jobspace/contact" class="text-blue-500 hover:text-blue-600">Contact Support</a>
                    </p>
                </div>
            </form>
        </div>
    </div>

    <script>
        // OTP Input Handling
        const otpInputs = document.querySelectorAll('.otp-input');
        const otpValue = document.getElementById('otpValue');
        const verifyBtn = document.getElementById('verifyBtn');

        otpInputs.forEach((input, index) => {
            input.addEventListener('input', function(e) {
                const value = e.target.value;
                
                // Only allow numbers
                if (!/^\d$/.test(value)) {
                    e.target.value = '';
                    return;
                }

                // Move to next input
                if (value && index < otpInputs.length - 1) {
                    otpInputs[index + 1].focus();
                }

                updateOTPValue();
            });

            input.addEventListener('keydown', function(e) {
                // Handle backspace
                if (e.key === 'Backspace' && !e.target.value && index > 0) {
                    otpInputs[index - 1].focus();
                }
                
                // Handle paste
                if (e.key === 'v' && (e.ctrlKey || e.metaKey)) {
                    e.preventDefault();
                    navigator.clipboard.readText().then(text => {
                        const digits = text.replace(/\D/g, '').slice(0, 6);
                        digits.split('').forEach((digit, i) => {
                            if (otpInputs[i]) {
                                otpInputs[i].value = digit;
                            }
                        });
                        updateOTPValue();
                        if (digits.length === 6) {
                            otpInputs[5].focus();
                        }
                    });
                }
            });
        });

        function updateOTPValue() {
            const otp = Array.from(otpInputs).map(input => input.value).join('');
            otpValue.value = otp;
            
            if (otp.length === 6) {
                verifyBtn.disabled = false;
                verifyBtn.className = 'w-full bg-blue-500 text-white py-3 rounded-lg font-semibold hover:bg-blue-600 transition duration-200 cursor-pointer';
                verifyBtn.textContent = 'Verify Account ✓';
            } else {
                verifyBtn.disabled = true;
                verifyBtn.className = 'w-full bg-gray-400 text-white py-3 rounded-lg font-semibold transition duration-200 cursor-not-allowed';
                verifyBtn.textContent = 'Verify Account';
            }
        }

        // Countdown Timer
        let timeLeft = 600; // 10 minutes in seconds
        const countdown = document.getElementById('countdown');

        function updateTimer() {
            const minutes = Math.floor(timeLeft / 60);
            const seconds = timeLeft % 60;
            countdown.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
            
            if (timeLeft <= 0) {
                countdown.textContent = 'Expired';
                countdown.className = 'font-semibold text-red-600';
                document.getElementById('timer').innerHTML = '<span class="text-red-600">⚠️ Code has expired. Please request a new one.</span>';
                
                // Disable form
                otpInputs.forEach(input => input.disabled = true);
                verifyBtn.disabled = true;
                verifyBtn.textContent = 'Code Expired';
                verifyBtn.className = 'w-full bg-red-400 text-white py-3 rounded-lg font-semibold cursor-not-allowed';
            } else {
                timeLeft--;
            }
        }

        // Update timer every second
        setInterval(updateTimer, 1000);

        // Resend OTP
        document.getElementById('resendOTP').addEventListener('click', function() {
            this.disabled = true;
            this.textContent = 'Sending...';
            
            fetch('/jobspace/auth/resend-otp', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Reset timer
                    timeLeft = 600;
                    countdown.className = 'font-semibold text-red-500';
                    
                    // Clear inputs
                    otpInputs.forEach(input => {
                        input.value = '';
                        input.disabled = false;
                    });
                    otpInputs[0].focus();
                    
                    // Show success message
                    alert('✅ New OTP sent successfully!');
                } else {
                    alert('❌ Failed to send OTP: ' + data.message);
                }
                
                this.disabled = false;
                this.textContent = '📱 Resend OTP Code';
            })
            .catch(error => {
                alert('❌ Error sending OTP');
                this.disabled = false;
                this.textContent = '📱 Resend OTP Code';
            });
        });

        // Check Email button
        document.getElementById('checkEmail').addEventListener('click', function() {
            alert('📧 Please check your email inbox and spam folder for the verification link. You can click the link instead of entering the code.');
        });

        // Auto-focus first input
        otpInputs[0].focus();

        // Form submission
        document.getElementById('otpForm').addEventListener('submit', function(e) {
            if (otpValue.value.length !== 6) {
                e.preventDefault();
                alert('Please enter the complete 6-digit code');
                return;
            }
            
            verifyBtn.disabled = true;
            verifyBtn.textContent = 'Verifying...';
        });
    </script>

<?php
include BASE_PATH . '/resources/views/components/footer/auth-footer.php';
?>

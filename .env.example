# Application Configuration
APP_NAME=JobSpace
APP_ENV=production
APP_DEBUG=false
APP_URL=http://localhost
APP_KEY=
APP_TIMEZONE=UTC
APP_LOCALE=en
APP_MAINTENANCE=false

# Performance Settings
MEMORY_LIMIT=512M
MAX_EXECUTION_TIME=30
OPCACHE_ENABLED=true
GZIP_COMPRESSION=true

# Database Configuration
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=jobspace
DB_USERNAME=root
DB_PASSWORD=

# Read Database (for read/write splitting)
DB_READ_HOST=127.0.0.1
DB_READ_PORT=3306
DB_READ_DATABASE=jobspace
DB_READ_USERNAME=root
DB_READ_PASSWORD=

# Database Performance
DB_MAX_CONNECTIONS=100
DB_TIMEOUT=30
DB_RETRY_ATTEMPTS=3
DB_RETRY_DELAY=1000
DB_LOG_QUERIES=false
DB_SLOW_QUERY_THRESHOLD=1000

# Database Pool
DB_POOL_ENABLED=true
DB_POOL_MIN=5
DB_POOL_MAX=50
DB_POOL_IDLE_TIMEOUT=300

# Read/Write Splitting
DB_READ_WRITE_SPLITTING=false

# Database Sharding
DB_SHARDING_ENABLED=false

# Cache Configuration
CACHE_DRIVER=file
CACHE_PATH=
CACHE_PREFIX=jobspace_cache
CACHE_TTL=3600
CACHE_COMPRESSION=true
CACHE_SERIALIZATION=php
CACHE_MAX_SIZE=100M
CACHE_CLEANUP_PROBABILITY=2
CACHE_CLEANUP_DIVISOR=100

# Cache Layers
CACHE_LAYERS_ENABLED=true

# Cache Warming
CACHE_WARMING_ENABLED=false

# Cache Monitoring
CACHE_MONITORING_ENABLED=true

# Cache Distributed
CACHE_DISTRIBUTED_ENABLED=false

# Redis Configuration (if using Redis cache)
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_PREFIX=jobspace
REDIS_CACHE_DB=1
REDIS_SESSION_DB=2

# Memcached Configuration (if using Memcached)
MEMCACHED_HOST=127.0.0.1
MEMCACHED_PORT=11211
MEMCACHED_PREFIX=jobspace

# Session Configuration
SESSION_DRIVER=file
SESSION_LIFETIME=120
SESSION_EXPIRE_ON_CLOSE=false
SESSION_ENCRYPT=false
SESSION_PATH=
SESSION_CONNECTION=
SESSION_COOKIE=jobspace_session
SESSION_DOMAIN=
SESSION_SECURE_COOKIE=false
SESSION_CHECK_IP=false
SESSION_MAX_CONCURRENT=5

# Session Monitoring
SESSION_MONITORING_ENABLED=true
SESSION_COMPRESSION=false

# Security Configuration
CSRF_ENABLED=true
RATE_LIMIT_ENABLED=true
RATE_LIMIT_MAX_ATTEMPTS=60
RATE_LIMIT_DECAY_MINUTES=1

# Password Requirements
PASSWORD_MIN_LENGTH=8
PASSWORD_MAX_LENGTH=128
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_NUMBERS=true
PASSWORD_REQUIRE_SYMBOLS=false
PASSWORD_PREVENT_COMMON=true
PASSWORD_PREVENT_PERSONAL_INFO=true
PASSWORD_HISTORY_LIMIT=5
PASSWORD_EXPIRE_DAYS=90

# Two-Factor Authentication
TWO_FACTOR_ENABLED=true
TWO_FACTOR_ISSUER=JobSpace

# Account Lockout
ACCOUNT_LOCKOUT_ENABLED=true
LOCKOUT_MAX_ATTEMPTS=5
LOCKOUT_DURATION=900

# IP Filtering
IP_FILTERING_ENABLED=false
IP_WHITELIST=
IP_BLACKLIST=

# Content Security Policy
CSP_ENABLED=true
CSP_REPORT_ONLY=false
CSP_REPORT_URI=/csp-report

# File Upload
UPLOAD_MAX_SIZE=********
UPLOAD_VIRUS_SCAN=false

# API Security
API_ALLOWED_ORIGINS=*

# Security Monitoring
SECURITY_MONITORING_ENABLED=true

# Logging Configuration
LOG_LEVEL=error
LOG_PATH=
LOG_MAX_FILES=30
LOG_MAX_SIZE=10M

# Mail Configuration
MAIL_DRIVER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD="wsnv nfhn yika gwkb"
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME=JobSpace

# Queue Configuration
QUEUE_DRIVER=database
QUEUE_CONNECTION=default

# Broadcasting
BROADCAST_DRIVER=log

# Filesystem
FILESYSTEM_DRIVER=local

# AWS S3 (if using S3 for file storage)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=

# Pusher (for real-time features)
PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

# Payment Gateways
STRIPE_KEY=
STRIPE_SECRET=
PAYPAL_CLIENT_ID=
PAYPAL_CLIENT_SECRET=
PAYPAL_MODE=sandbox

# Social Login
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
FACEBOOK_CLIENT_ID=
FACEBOOK_CLIENT_SECRET=
TWITTER_CLIENT_ID=
TWITTER_CLIENT_SECRET=

# SMS Service
SMS_DRIVER=twilio
TWILIO_SID=
TWILIO_TOKEN=
TWILIO_FROM=

# Analytics
GOOGLE_ANALYTICS_ID=
FACEBOOK_PIXEL_ID=

# CDN
CDN_URL=
CDN_ENABLED=false

# Monitoring & Analytics
SENTRY_DSN=
NEW_RELIC_LICENSE_KEY=

# Development Tools
TELESCOPE_ENABLED=false
DEBUGBAR_ENABLED=false

<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "Fixing users table structure...\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=jobspace', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check current table structure
    echo "Current users table structure:\n";
    $stmt = $pdo->query("DESCRIBE users");
    $existingColumns = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $existingColumns[] = $row['Field'];
        echo "- {$row['Field']}: {$row['Type']}\n";
    }
    
    // Add missing columns if they don't exist
    $columnsToAdd = [
        'email_verified' => "ALTER TABLE users ADD COLUMN email_verified BOOLEAN DEFAULT FALSE",
        'referral_code_generated' => "ALTER TABLE users ADD COLUMN referral_code_generated VARCHAR(50) NULL",
        'referral_code_used' => "ALTER TABLE users ADD COLUMN referral_code_used VARCHAR(50) NULL"
    ];
    
    foreach ($columnsToAdd as $column => $sql) {
        if (!in_array($column, $existingColumns)) {
            echo "\nAdding column: $column\n";
            $pdo->exec($sql);
            echo "✅ Column $column added successfully!\n";
        } else {
            echo "\n✅ Column $column already exists\n";
        }
    }
    
    // Update email_verified_at column to be nullable if it exists
    if (in_array('email_verified_at', $existingColumns)) {
        echo "\nUpdating email_verified_at column...\n";
        $pdo->exec("ALTER TABLE users MODIFY COLUMN email_verified_at TIMESTAMP NULL");
        echo "✅ email_verified_at column updated!\n";
    }
    
    echo "\n✅ Users table structure fixed!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

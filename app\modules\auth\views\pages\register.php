<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Step 1 | JobSpace</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-gray-800 mb-2">JobSpace</h1>
            <p class="text-gray-600">Create your account - Step 1 of 2</p>
        </div>

        <!-- Progress Bar -->
        <div class="max-w-md mx-auto mb-8">
            <div class="flex items-center">
                <div class="flex-1 h-2 bg-blue-500 rounded-l"></div>
                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold">1</div>
                <div class="flex-1 h-2 bg-gray-300"></div>
                <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center text-gray-600 text-sm font-bold">2</div>
                <div class="flex-1 h-2 bg-gray-300 rounded-r"></div>
            </div>
            <div class="flex justify-between mt-2 text-sm">
                <span class="text-blue-600 font-medium">Personal Info</span>
                <span class="text-gray-500">Additional Details</span>
            </div>
        </div>

        <!-- Registration Form -->
        <div class="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-2xl font-bold text-gray-800 mb-6 text-center">Personal Information</h2>
            
            <form method="POST" action="/jobspace/auth/process-step1" x-data="registrationForm()">
                <input type="hidden" name="csrf_token" value="<?= \App\Core\Security::getCSRFToken() ?>">
                
                <!-- First Name -->
                <div class="mb-4">
                    <label for="first_name" class="block text-sm font-medium text-gray-700 mb-2">First Name *</label>
                    <input type="text" id="first_name" name="first_name" 
                           value="<?= htmlspecialchars($old_data['first_name'] ?? '') ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           required maxlength="50">
                    <?php if (isset($errors['first_name'])): ?>
                        <p class="text-red-500 text-sm mt-1"><?= htmlspecialchars($errors['first_name']) ?></p>
                    <?php endif; ?>
                </div>

                <!-- Last Name -->
                <div class="mb-4">
                    <label for="last_name" class="block text-sm font-medium text-gray-700 mb-2">Last Name *</label>
                    <input type="text" id="last_name" name="last_name" 
                           value="<?= htmlspecialchars($old_data['last_name'] ?? '') ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           required maxlength="50">
                    <?php if (isset($errors['last_name'])): ?>
                        <p class="text-red-500 text-sm mt-1"><?= htmlspecialchars($errors['last_name']) ?></p>
                    <?php endif; ?>
                </div>

                <!-- Email -->
                <div class="mb-4">
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                    <input type="email" id="email" name="email" 
                           value="<?= htmlspecialchars($old_data['email'] ?? '') ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           required x-on:blur="checkEmailAvailability">
                    <div x-show="emailChecking" class="text-blue-500 text-sm mt-1">Checking availability...</div>
                    <div x-show="emailAvailable === false" class="text-red-500 text-sm mt-1">This email is already registered</div>
                    <div x-show="emailAvailable === true" class="text-green-500 text-sm mt-1">Email is available</div>
                    <?php if (isset($errors['email'])): ?>
                        <p class="text-red-500 text-sm mt-1"><?= htmlspecialchars($errors['email']) ?></p>
                    <?php endif; ?>
                </div>

                <!-- Phone -->
                <div class="mb-4">
                    <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number *</label>
                    <input type="tel" id="phone" name="phone" 
                           value="<?= htmlspecialchars($old_data['phone'] ?? '') ?>"
                           placeholder="01XXXXXXXXX"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           required pattern="01[3-9]\d{8}">
                    <p class="text-gray-500 text-sm mt-1">Enter Bangladeshi phone number (e.g., 01712345678)</p>
                    <?php if (isset($errors['phone'])): ?>
                        <p class="text-red-500 text-sm mt-1"><?= htmlspecialchars($errors['phone']) ?></p>
                    <?php endif; ?>
                </div>

                <!-- Date of Birth -->
                <div class="mb-4">
                    <label for="date_of_birth" class="block text-sm font-medium text-gray-700 mb-2">Date of Birth *</label>
                    <input type="date" id="date_of_birth" name="date_of_birth" 
                           value="<?= htmlspecialchars($old_data['date_of_birth'] ?? '') ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           required max="<?= date('Y-m-d', strtotime('-13 years')) ?>">
                    <?php if (isset($errors['date_of_birth'])): ?>
                        <p class="text-red-500 text-sm mt-1"><?= htmlspecialchars($errors['date_of_birth']) ?></p>
                    <?php endif; ?>
                </div>

                <!-- Gender -->
                <div class="mb-4">
                    <label for="gender" class="block text-sm font-medium text-gray-700 mb-2">Gender *</label>
                    <select id="gender" name="gender" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            required>
                        <option value="">Select Gender</option>
                        <option value="Male" <?= ($old_data['gender'] ?? '') === 'Male' ? 'selected' : '' ?>>Male</option>
                        <option value="Female" <?= ($old_data['gender'] ?? '') === 'Female' ? 'selected' : '' ?>>Female</option>
                        <option value="Other" <?= ($old_data['gender'] ?? '') === 'Other' ? 'selected' : '' ?>>Other</option>
                    </select>
                    <?php if (isset($errors['gender'])): ?>
                        <p class="text-red-500 text-sm mt-1"><?= htmlspecialchars($errors['gender']) ?></p>
                    <?php endif; ?>
                </div>

                <!-- Password -->
                <div class="mb-4">
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">Password *</label>
                    <input type="password" id="password" name="password" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           required minlength="8" x-model="password">
                    <p class="text-gray-500 text-sm mt-1">Minimum 8 characters</p>
                    <?php if (isset($errors['password'])): ?>
                        <p class="text-red-500 text-sm mt-1"><?= htmlspecialchars($errors['password']) ?></p>
                    <?php endif; ?>
                </div>

                <!-- Confirm Password -->
                <div class="mb-6">
                    <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-2">Confirm Password *</label>
                    <input type="password" id="confirm_password" name="confirm_password" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           required x-model="confirmPassword">
                    <div x-show="password && confirmPassword && password !== confirmPassword" class="text-red-500 text-sm mt-1">
                        Passwords do not match
                    </div>
                    <?php if (isset($errors['confirm_password'])): ?>
                        <p class="text-red-500 text-sm mt-1"><?= htmlspecialchars($errors['confirm_password']) ?></p>
                    <?php endif; ?>
                </div>

                <!-- Submit Button -->
                <button type="submit" 
                        class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-200">
                    Continue to Step 2
                </button>
            </form>

            <!-- Login Link -->
            <div class="text-center mt-6">
                <p class="text-gray-600">Already have an account? 
                    <a href="/jobspace/auth/login" class="text-blue-600 hover:text-blue-800 font-medium">Login here</a>
                </p>
            </div>
        </div>
    </div>

    <script>
        function registrationForm() {
            return {
                password: '',
                confirmPassword: '',
                emailChecking: false,
                emailAvailable: null,
                
                async checkEmailAvailability() {
                    const email = document.getElementById('email').value;
                    if (!email || !email.includes('@')) return;
                    
                    this.emailChecking = true;
                    this.emailAvailable = null;
                    
                    try {
                        const response = await fetch('/jobspace/auth/check-email', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: `email=${encodeURIComponent(email)}`
                        });
                        
                        const data = await response.json();
                        this.emailAvailable = data.available;
                    } catch (error) {
                        console.error('Error checking email:', error);
                    } finally {
                        this.emailChecking = false;
                    }
                }
            }
        }
    </script>
</body>
</html>

<?php 
$title = "Reset Password | JobSpace";
$bodyClass = "auth-container";
include BASE_PATH . '/resources/views/components/header/auth-header.php'; 
?>
    <div class="min-h-screen flex items-center justify-center p-4">
        <div class="bg-white rounded-2xl shadow-2xl w-full max-w-md">
            <!-- Header -->
            <div class="p-8 pb-6">
                <div class="text-center mb-8">
                    <div class="text-4xl mb-4">🔑</div>
                    <h1 class="text-2xl font-bold text-gray-800 mb-2">Reset Your Password</h1>
                    <p class="text-gray-600">Enter your new password below</p>
                </div>
            </div>

            <!-- Reset Password Form -->
            <form action="/jobspace/auth/process-reset-password" method="POST" class="px-8 pb-8" id="resetForm">
                <input type="hidden" name="token" value="<?= htmlspecialchars($token ?? '') ?>">
                
                <!-- New Password -->
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-semibold mb-2">New Password</label>
                    <div class="relative">
                        <input type="password" name="password" id="password" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-green-500 transition pr-12"
                               placeholder="Enter your new password">
                        <button type="button" id="togglePassword" 
                                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700">
                            <span id="eyeIcon">👁️</span>
                        </button>
                    </div>
                    <div class="text-xs text-gray-500 mt-1">
                        Must be at least 8 characters with uppercase, lowercase, and numbers
                    </div>
                </div>

                <!-- Confirm New Password -->
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-semibold mb-2">Confirm New Password</label>
                    <div class="relative">
                        <input type="password" name="confirm_password" id="confirm_password" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-green-500 transition pr-12"
                               placeholder="Confirm your new password">
                        <button type="button" id="toggleConfirmPassword" 
                                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700">
                            <span id="eyeIcon2">👁️</span>
                        </button>
                    </div>
                    <div id="password-match" class="text-xs mt-1"></div>
                </div>

                <!-- Password Strength Indicator -->
                <div class="mb-6">
                    <div class="text-sm font-semibold text-gray-700 mb-2">Password Strength:</div>
                    <div class="flex space-x-1 mb-2">
                        <div id="strength-1" class="flex-1 h-2 bg-gray-200 rounded"></div>
                        <div id="strength-2" class="flex-1 h-2 bg-gray-200 rounded"></div>
                        <div id="strength-3" class="flex-1 h-2 bg-gray-200 rounded"></div>
                        <div id="strength-4" class="flex-1 h-2 bg-gray-200 rounded"></div>
                    </div>
                    <div id="strength-text" class="text-xs text-gray-500">Enter a password to see strength</div>
                </div>

                <!-- Submit Button -->
                <button type="submit" id="resetBtn"
                        class="w-full bg-green-500 text-white py-3 rounded-lg font-semibold hover:bg-green-600 transition duration-200">
                    Reset Password
                </button>

                <!-- Back to Login -->
                <div class="text-center mt-6">
                    <a href="/jobspace/auth/login" 
                       class="text-gray-600 hover:text-gray-800 font-semibold flex items-center justify-center">
                        ← Back to Login
                    </a>
                </div>
            </form>

            <!-- Security Notice -->
            <div class="px-8 pb-8">
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <h3 class="font-semibold text-yellow-800 mb-2">🔒 Security Notice</h3>
                    <div class="text-sm text-yellow-700 space-y-1">
                        <p>• Choose a strong, unique password</p>
                        <p>• Don't reuse passwords from other sites</p>
                        <p>• Consider using a password manager</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Password visibility toggles
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const eyeIcon = document.getElementById('eyeIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                eyeIcon.textContent = '🙈';
            } else {
                passwordInput.type = 'password';
                eyeIcon.textContent = '👁️';
            }
        });

        document.getElementById('toggleConfirmPassword').addEventListener('click', function() {
            const passwordInput = document.getElementById('confirm_password');
            const eyeIcon = document.getElementById('eyeIcon2');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                eyeIcon.textContent = '🙈';
            } else {
                passwordInput.type = 'password';
                eyeIcon.textContent = '👁️';
            }
        });

        // Password strength checker
        function checkPasswordStrength(password) {
            let strength = 0;
            const checks = {
                length: password.length >= 8,
                lowercase: /[a-z]/.test(password),
                uppercase: /[A-Z]/.test(password),
                numbers: /[0-9]/.test(password),
                symbols: /[^a-zA-Z0-9]/.test(password)
            };

            strength = Object.values(checks).filter(Boolean).length;

            // Update strength bars
            const bars = ['strength-1', 'strength-2', 'strength-3', 'strength-4'];
            const colors = ['bg-red-400', 'bg-orange-400', 'bg-yellow-400', 'bg-green-400'];
            const texts = ['Very Weak', 'Weak', 'Good', 'Strong'];

            bars.forEach((bar, index) => {
                const element = document.getElementById(bar);
                element.className = 'flex-1 h-2 rounded ' + (index < strength ? colors[Math.min(strength - 1, 3)] : 'bg-gray-200');
            });

            const strengthText = document.getElementById('strength-text');
            if (password.length === 0) {
                strengthText.textContent = 'Enter a password to see strength';
                strengthText.className = 'text-xs text-gray-500';
            } else {
                strengthText.textContent = texts[Math.min(strength - 1, 3)] || 'Very Weak';
                strengthText.className = 'text-xs ' + (strength >= 3 ? 'text-green-600' : strength >= 2 ? 'text-yellow-600' : 'text-red-600');
            }

            return strength;
        }

        // Password confirmation check
        function checkPasswordMatch() {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            const feedback = document.getElementById('password-match');
            
            if (confirmPassword.length > 0) {
                if (password === confirmPassword) {
                    feedback.className = 'text-green-500 text-xs mt-1';
                    feedback.textContent = '✓ Passwords match';
                    return true;
                } else {
                    feedback.className = 'text-red-500 text-xs mt-1';
                    feedback.textContent = '✗ Passwords do not match';
                    return false;
                }
            } else {
                feedback.textContent = '';
                return false;
            }
        }

        // Event listeners
        document.getElementById('password').addEventListener('input', function() {
            checkPasswordStrength(this.value);
            checkPasswordMatch();
        });

        document.getElementById('confirm_password').addEventListener('input', checkPasswordMatch);

        // Form submission
        document.getElementById('resetForm').addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            const submitBtn = document.getElementById('resetBtn');
            
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('Passwords do not match');
                return;
            }
            
            if (checkPasswordStrength(password) < 3) {
                e.preventDefault();
                alert('Please choose a stronger password');
                return;
            }
            
            submitBtn.disabled = true;
            submitBtn.textContent = 'Resetting Password...';
            submitBtn.className = 'w-full bg-green-400 text-white py-3 rounded-lg font-semibold cursor-not-allowed';
        });

        // Auto-focus password field
        document.getElementById('password').focus();
    </script>

<?php
include BASE_PATH . '/resources/views/components/footer/auth-footer.php';
?>

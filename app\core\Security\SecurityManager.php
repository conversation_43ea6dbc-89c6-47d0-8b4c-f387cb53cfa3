<?php

namespace App\Core\Security;

use App\Core\Config\ConfigManager;
use App\Core\Http\Request;
use Exception;

/**
 * Security Manager
 * Handles authentication, authorization, and security features
 */
class SecurityManager
{
    private ConfigManager $config;
    private array $rateLimits = [];

    public function __construct(ConfigManager $config)
    {
        $this->config = $config;
    }

    /**
     * Validate incoming request
     */
    public function validateRequest(Request $request): void
    {
        // Validate request size
        $this->validateRequestSize($request);
        
        // Validate headers
        $this->validateHeaders($request);
        
        // Check for malicious patterns
        $this->checkMaliciousPatterns($request);
    }

    /**
     * Check rate limiting
     */
    public function checkRateLimit(Request $request): void
    {
        if (!$this->config->get('security.rate_limit.enabled', true)) {
            return;
        }

        $identifier = $this->getRateLimitIdentifier($request);
        $maxAttempts = $this->config->get('security.rate_limit.max_attempts', 60);
        $decayMinutes = $this->config->get('security.rate_limit.decay_minutes', 1);

        $key = "rate_limit:{$identifier}";
        $attempts = $this->getRateLimitAttempts($key);

        if ($attempts >= $maxAttempts) {
            throw new SecurityException('Rate limit exceeded', 429);
        }

        $this->incrementRateLimit($key, $decayMinutes);
    }

    /**
     * Generate CSRF token
     */
    public function generateCsrfToken(): string
    {
        $token = bin2hex(random_bytes(32));
        
        if (session_status() === PHP_SESSION_ACTIVE) {
            $_SESSION['_csrf_token'] = $token;
        }
        
        return $token;
    }

    /**
     * Verify CSRF token
     */
    public function verifyCsrfToken(Request $request): bool
    {
        $tokenName = $this->config->get('security.csrf_token_name', '_token');
        $headerName = $this->config->get('security.csrf_header_name', 'X-CSRF-TOKEN');
        
        $token = $request->input($tokenName) ?? $request->header($headerName);
        $sessionToken = $_SESSION['_csrf_token'] ?? null;
        
        return $token && $sessionToken && hash_equals($sessionToken, $token);
    }

    /**
     * Hash password
     */
    public function hashPassword(string $password): string
    {
        $this->validatePassword($password);
        return password_hash($password, PASSWORD_ARGON2ID, [
            'memory_cost' => 65536, // 64 MB
            'time_cost' => 4,       // 4 iterations
            'threads' => 3          // 3 threads
        ]);
    }

    /**
     * Verify password
     */
    public function verifyPassword(string $password, string $hash): bool
    {
        return password_verify($password, $hash);
    }

    /**
     * Validate password strength
     */
    public function validatePassword(string $password): void
    {
        $config = $this->config->get('security.password', []);
        $minLength = $config['min_length'] ?? 8;
        
        if (strlen($password) < $minLength) {
            throw new SecurityException("Password must be at least {$minLength} characters long");
        }

        if ($config['require_uppercase'] ?? true) {
            if (!preg_match('/[A-Z]/', $password)) {
                throw new SecurityException('Password must contain at least one uppercase letter');
            }
        }

        if ($config['require_lowercase'] ?? true) {
            if (!preg_match('/[a-z]/', $password)) {
                throw new SecurityException('Password must contain at least one lowercase letter');
            }
        }

        if ($config['require_numbers'] ?? true) {
            if (!preg_match('/[0-9]/', $password)) {
                throw new SecurityException('Password must contain at least one number');
            }
        }

        if ($config['require_symbols'] ?? false) {
            if (!preg_match('/[^A-Za-z0-9]/', $password)) {
                throw new SecurityException('Password must contain at least one special character');
            }
        }
    }

    /**
     * Sanitize input
     */
    public function sanitizeInput($input): string
    {
        if (is_array($input)) {
            return array_map([$this, 'sanitizeInput'], $input);
        }
        
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }

    /**
     * Validate email
     */
    public function validateEmail(string $email): bool
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * Generate secure random token
     */
    public function generateToken(int $length = 32): string
    {
        return bin2hex(random_bytes($length));
    }

    /**
     * Encrypt data
     */
    public function encrypt(string $data, string $key = null): string
    {
        $key = $key ?? $this->config->get('app.key');
        $cipher = $this->config->get('app.cipher', 'AES-256-CBC');
        
        if (!$key) {
            throw new SecurityException('Encryption key not set');
        }

        $iv = random_bytes(openssl_cipher_iv_length($cipher));
        $encrypted = openssl_encrypt($data, $cipher, $key, 0, $iv);
        
        return base64_encode($iv . $encrypted);
    }

    /**
     * Decrypt data
     */
    public function decrypt(string $data, string $key = null): string
    {
        $key = $key ?? $this->config->get('app.key');
        $cipher = $this->config->get('app.cipher', 'AES-256-CBC');
        
        if (!$key) {
            throw new SecurityException('Encryption key not set');
        }

        $data = base64_decode($data);
        $ivLength = openssl_cipher_iv_length($cipher);
        $iv = substr($data, 0, $ivLength);
        $encrypted = substr($data, $ivLength);
        
        $decrypted = openssl_decrypt($encrypted, $cipher, $key, 0, $iv);
        
        if ($decrypted === false) {
            throw new SecurityException('Decryption failed');
        }
        
        return $decrypted;
    }

    /**
     * Validate request size
     */
    private function validateRequestSize(Request $request): void
    {
        $maxSize = $this->config->get('security.max_request_size', 10485760); // 10MB
        $contentLength = $request->header('content-length');
        
        if ($contentLength && $contentLength > $maxSize) {
            throw new SecurityException('Request too large', 413);
        }
    }

    /**
     * Validate headers
     */
    private function validateHeaders(Request $request): void
    {
        $userAgent = $request->getUserAgent();
        
        // Block empty user agents
        if (empty($userAgent)) {
            throw new SecurityException('Invalid user agent', 400);
        }
        
        // Block known malicious user agents
        $blockedAgents = [
            'sqlmap',
            'nikto',
            'nessus',
            'openvas',
            'nmap'
        ];
        
        foreach ($blockedAgents as $agent) {
            if (stripos($userAgent, $agent) !== false) {
                throw new SecurityException('Blocked user agent', 403);
            }
        }
    }

    /**
     * Check for malicious patterns
     */
    private function checkMaliciousPatterns(Request $request): void
    {
        $patterns = [
            // SQL Injection
            '/(\bunion\b.*\bselect\b)|(\bselect\b.*\bunion\b)/i',
            '/\b(select|insert|update|delete|drop|create|alter)\b.*\b(from|into|table|database)\b/i',
            
            // XSS
            '/<script[^>]*>.*?<\/script>/i',
            '/javascript:/i',
            '/on\w+\s*=/i',
            
            // Path Traversal
            '/\.\.[\/\\\\]/i',
            '/\.(exe|bat|cmd|com|pif|scr|vbs|js)$/i',
            
            // Command Injection
            '/[;&|`$(){}]/i'
        ];

        $input = json_encode($request->all());
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $input)) {
                throw new SecurityException('Malicious pattern detected', 403);
            }
        }
    }

    /**
     * Get rate limit identifier
     */
    private function getRateLimitIdentifier(Request $request): string
    {
        return $request->getClientIp();
    }

    /**
     * Get rate limit attempts
     */
    private function getRateLimitAttempts(string $key): int
    {
        return $this->rateLimits[$key]['attempts'] ?? 0;
    }

    /**
     * Increment rate limit
     */
    private function incrementRateLimit(string $key, int $decayMinutes): void
    {
        $now = time();
        $resetTime = $now + ($decayMinutes * 60);
        
        if (!isset($this->rateLimits[$key]) || $now > $this->rateLimits[$key]['reset_time']) {
            $this->rateLimits[$key] = [
                'attempts' => 1,
                'reset_time' => $resetTime
            ];
        } else {
            $this->rateLimits[$key]['attempts']++;
        }
    }

    /**
     * Generate JWT token
     */
    public function generateJwtToken(array $payload, int $expiry = 3600): string
    {
        $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
        $payload['exp'] = time() + $expiry;
        $payload = json_encode($payload);
        
        $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
        $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));
        
        $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, $this->config->get('app.key'), true);
        $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));
        
        return $base64Header . "." . $base64Payload . "." . $base64Signature;
    }

    /**
     * Verify JWT token
     */
    public function verifyJwtToken(string $token): ?array
    {
        $parts = explode('.', $token);
        
        if (count($parts) !== 3) {
            return null;
        }
        
        [$header, $payload, $signature] = $parts;
        
        $validSignature = hash_hmac('sha256', $header . "." . $payload, $this->config->get('app.key'), true);
        $validSignature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($validSignature));
        
        if (!hash_equals($signature, $validSignature)) {
            return null;
        }
        
        $payload = json_decode(base64_decode(str_replace(['-', '_'], ['+', '/'], $payload)), true);
        
        if (isset($payload['exp']) && time() > $payload['exp']) {
            return null;
        }
        
        return $payload;
    }
}

/**
 * Security Exception
 */
class SecurityException extends Exception
{
    //
}

<?php

namespace App\Modules\Auth\Services;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use P<PERSON><PERSON>ailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

class EmailService
{
    private array $config;
    private array $emailConfig;
    
    public function __construct()
    {
        $this->config = include BASE_PATH . '/app/modules/auth/config/auth.php';
        $this->emailConfig = [
            'host' => $_ENV['MAIL_HOST'] ?? 'smtp.gmail.com',
            'port' => $_ENV['MAIL_PORT'] ?? 587,
            'username' => $_ENV['MAIL_USERNAME'] ?? '',
            'password' => $_ENV['MAIL_PASSWORD'] ?? '',
            'encryption' => $_ENV['MAIL_ENCRYPTION'] ?? 'tls',
            'from_address' => $_ENV['MAIL_FROM_ADDRESS'] ?? '<EMAIL>',
            'from_name' => $_ENV['MAIL_FROM_NAME'] ?? 'JobSpace'
        ];
    }
    
    /**
     * Send verification email with link and OTP
     */
    public function sendVerificationEmail(string $email, string $name, string $verificationToken, string $otp): bool
    {
        $subject = 'Verify Your JobSpace Account';
        
        // Verification link
        $verificationLink = $this->getBaseUrl() . "/auth/verify-email?token=" . $verificationToken;
        
        // Load email template
        $emailContent = $this->loadEmailTemplate('verification', [
            'name' => $name,
            'verification_link' => $verificationLink,
            'otp' => $otp,
            'otp_expiry' => $this->config['otp']['expiry'] / 60 // in minutes
        ]);
        
        return $this->sendEmail($email, $subject, $emailContent);
    }
    
    /**
     * Send OTP email
     */
    public function sendOTPEmail(string $email, string $name, string $otp): bool
    {
        $subject = 'Your JobSpace Verification Code';
        
        $emailContent = $this->loadEmailTemplate('otp', [
            'name' => $name,
            'otp' => $otp,
            'otp_expiry' => $this->config['otp']['expiry'] / 60 // in minutes
        ]);
        
        return $this->sendEmail($email, $subject, $emailContent);
    }
    
    /**
     * Send welcome email
     */
    public function sendWelcomeEmail(string $email, string $name, array $userData): bool
    {
        $subject = 'Welcome to JobSpace!';
        
        $emailContent = $this->loadEmailTemplate('welcome', [
            'name' => $name,
            'username' => $userData['username'],
            'role' => $userData['role'],
            'referral_code' => $userData['referral_code_generated'] ?? '',
            'login_url' => $this->getBaseUrl() . '/auth/login'
        ]);
        
        return $this->sendEmail($email, $subject, $emailContent);
    }
    
    /**
     * Send password reset email
     */
    public function sendPasswordResetEmail(string $email, string $name, string $resetToken): bool
    {
        $subject = 'Reset Your JobSpace Password';
        
        $resetLink = $this->getBaseUrl() . "/auth/reset-password?token=" . $resetToken;
        
        $emailContent = $this->loadEmailTemplate('password_reset', [
            'name' => $name,
            'reset_link' => $resetLink,
            'expiry' => $this->config['password']['reset_token_expiry'] / 60 // in minutes
        ]);
        
        return $this->sendEmail($email, $subject, $emailContent);
    }
    
    /**
     * Load email template
     */
    private function loadEmailTemplate(string $template, array $data): string
    {
        $templatePath = BASE_PATH . '/app/modules/auth/views/emails/' . $template . '.php';
        
        if (!file_exists($templatePath)) {
            return $this->getDefaultEmailTemplate($template, $data);
        }
        
        // Extract data to variables
        extract($data);
        
        // Start output buffering
        ob_start();
        
        // Include template
        include $templatePath;
        
        // Return rendered content
        return ob_get_clean();
    }
    
    /**
     * Get default email template if custom template doesn't exist
     */
    private function getDefaultEmailTemplate(string $template, array $data): string
    {
        switch ($template) {
            case 'verification':
                return $this->getVerificationEmailTemplate($data);
            case 'otp':
                return $this->getOTPEmailTemplate($data);
            case 'welcome':
                return $this->getWelcomeEmailTemplate($data);
            case 'password_reset':
                return $this->getPasswordResetEmailTemplate($data);
            default:
                return 'Email template not found.';
        }
    }
    
    /**
     * Default verification email template
     */
    private function getVerificationEmailTemplate(array $data): string
    {
        return "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
            <h2 style='color: #333;'>Verify Your JobSpace Account</h2>
            <p>Hello {$data['name']},</p>
            <p>Thank you for registering with JobSpace! Please verify your email address using one of the methods below:</p>
            
            <div style='background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 8px;'>
                <h3 style='color: #495057;'>Method 1: Click the verification link</h3>
                <a href='{$data['verification_link']}' style='background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;'>Verify Email</a>
            </div>
            
            <div style='background: #e9ecef; padding: 20px; margin: 20px 0; border-radius: 8px;'>
                <h3 style='color: #495057;'>Method 2: Enter this verification code</h3>
                <div style='font-size: 24px; font-weight: bold; color: #007bff; letter-spacing: 2px;'>{$data['otp']}</div>
                <p style='font-size: 14px; color: #6c757d;'>This code expires in {$data['otp_expiry']} minutes.</p>
            </div>
            
            <p style='color: #6c757d; font-size: 14px;'>If you didn't create this account, please ignore this email.</p>
        </div>";
    }
    
    /**
     * Default OTP email template
     */
    private function getOTPEmailTemplate(array $data): string
    {
        return "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
            <h2 style='color: #333;'>Your Verification Code</h2>
            <p>Hello {$data['name']},</p>
            <p>Your verification code is:</p>
            
            <div style='background: #f8f9fa; padding: 30px; margin: 20px 0; border-radius: 8px; text-align: center;'>
                <div style='font-size: 32px; font-weight: bold; color: #007bff; letter-spacing: 3px;'>{$data['otp']}</div>
                <p style='font-size: 14px; color: #6c757d; margin-top: 10px;'>This code expires in {$data['otp_expiry']} minutes.</p>
            </div>
            
            <p style='color: #6c757d; font-size: 14px;'>If you didn't request this code, please ignore this email.</p>
        </div>";
    }
    
    /**
     * Default welcome email template
     */
    private function getWelcomeEmailTemplate(array $data): string
    {
        return "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
            <h2 style='color: #333;'>Welcome to JobSpace! 🚀</h2>
            <p>Hello {$data['name']},</p>
            <p>Your account has been successfully created and verified!</p>
            
            <div style='background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 8px;'>
                <h3 style='color: #495057;'>Account Details</h3>
                <p><strong>Username:</strong> {$data['username']}</p>
                <p><strong>Role:</strong> {$data['role']}</p>
                <p><strong>Your Referral Code:</strong> {$data['referral_code']}</p>
            </div>
            
            <div style='text-align: center; margin: 30px 0;'>
                <a href='{$data['login_url']}' style='background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;'>Login to Your Account</a>
            </div>
            
            <p>Thank you for joining JobSpace!</p>
        </div>";
    }
    
    /**
     * Default password reset email template
     */
    private function getPasswordResetEmailTemplate(array $data): string
    {
        return "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
            <h2 style='color: #333;'>Reset Your Password</h2>
            <p>Hello {$data['name']},</p>
            <p>You requested to reset your password. Click the button below to reset it:</p>
            
            <div style='text-align: center; margin: 30px 0;'>
                <a href='{$data['reset_link']}' style='background: #dc3545; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;'>Reset Password</a>
            </div>
            
            <p style='color: #6c757d; font-size: 14px;'>This link expires in {$data['expiry']} minutes. If you didn't request this, please ignore this email.</p>
        </div>";
    }
    
    /**
     * Send email using PHPMailer
     */
    private function sendEmail(string $to, string $subject, string $content): bool
    {
        // Always log email attempts for debugging
        error_log("=== EMAIL ATTEMPT ===");
        error_log("TO: $to");
        error_log("SUBJECT: $subject");
        error_log("FROM: " . $this->emailConfig['from_address']);
        error_log("SMTP HOST: " . $this->emailConfig['host']);
        error_log("SMTP USERNAME: " . $this->emailConfig['username']);
        error_log("SMTP PASSWORD SET: " . (!empty($this->emailConfig['password']) ? 'YES' : 'NO'));

        // If email credentials are not configured, log instead
        if (empty($this->emailConfig['username']) || empty($this->emailConfig['password'])) {
            error_log("EMAIL CREDENTIALS NOT CONFIGURED - LOGGING ONLY");
            error_log("CONTENT: " . strip_tags($content));
            return true; // Return true for development
        }

        try {
            $mail = new PHPMailer(true);

            // Enable verbose debug output for troubleshooting
            $mail->SMTPDebug = 0; // Set to 2 for detailed debugging
            $mail->Debugoutput = function($str, $level) {
                error_log("PHPMailer Debug: $str");
            };

            // Server settings
            $mail->isSMTP();
            $mail->Host = $this->emailConfig['host'];
            $mail->SMTPAuth = true;
            $mail->Username = $this->emailConfig['username'];
            $mail->Password = $this->emailConfig['password'];
            $mail->SMTPSecure = $this->emailConfig['encryption'] === 'tls' ? PHPMailer::ENCRYPTION_STARTTLS : PHPMailer::ENCRYPTION_SMTPS;
            $mail->Port = $this->emailConfig['port'];

            // Recipients
            $mail->setFrom($this->emailConfig['from_address'], $this->emailConfig['from_name']);
            $mail->addAddress($to);
            $mail->addReplyTo($this->emailConfig['from_address'], $this->emailConfig['from_name']);

            // Content
            $mail->isHTML(true);
            $mail->Subject = $subject;
            $mail->Body = $content;
            $mail->AltBody = strip_tags($content);

            // Send email
            $result = $mail->send();

            if ($result) {
                error_log("✅ Email sent successfully to: $to");
            } else {
                error_log("❌ Email sending failed but no exception thrown");
            }

            return $result;

        } catch (Exception $e) {
            error_log("❌ Email sending failed with exception:");
            error_log("PHPMailer Error: " . $mail->ErrorInfo);
            error_log("Exception Message: " . $e->getMessage());
            error_log("Exception Trace: " . $e->getTraceAsString());

            // Fallback: log the email content
            error_log("FALLBACK - EMAIL CONTENT:");
            error_log("CONTENT: " . strip_tags($content));

            return false;
        }
    }
    
    /**
     * Get base URL
     */
    private function getBaseUrl(): string
    {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        return $protocol . '://' . $host . '/jobspace';
    }
}

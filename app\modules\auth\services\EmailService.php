<?php

namespace App\Modules\Auth\Services;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use PHP<PERSON>ailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

class EmailService
{
    private array $config;
    private array $emailConfig;
    
    public function __construct()
    {
        $this->config = include BASE_PATH . '/app/modules/auth/config/auth.php';
        $this->emailConfig = [
            'host' => $_ENV['MAIL_HOST'] ?? 'smtp.gmail.com',
            'port' => $_ENV['MAIL_PORT'] ?? 587,
            'username' => $_ENV['MAIL_USERNAME'] ?? '',
            'password' => $_ENV['MAIL_PASSWORD'] ?? '',
            'encryption' => $_ENV['MAIL_ENCRYPTION'] ?? 'tls',
            'from_address' => $_ENV['MAIL_FROM_ADDRESS'] ?? '<EMAIL>',
            'from_name' => $_ENV['MAIL_FROM_NAME'] ?? 'JobSpace'
        ];
    }
    
    /**
     * Send verification email with OTP
     */
    public function sendVerificationEmail(string $email, string $name, string $verificationToken, string $otp): bool
    {
        $subject = 'Verify Your JobSpace Account';
        
        // Verification link
        $verificationLink = $this->getBaseUrl() . "/auth/verify-email?token=" . $verificationToken;
        
        // Email content
        $emailContent = $this->getVerificationEmailTemplate([
            'name' => $name,
            'verification_link' => $verificationLink,
            'otp' => $otp,
            'otp_expiry' => $this->config['otp']['expiry'] / 60 // in minutes
        ]);
        
        return $this->sendEmail($email, $subject, $emailContent);
    }
    
    /**
     * Send OTP email
     */
    public function sendOTPEmail(string $email, string $name, string $otp): bool
    {
        $subject = 'Your JobSpace Verification Code';
        
        $emailContent = $this->getOTPEmailTemplate([
            'name' => $name,
            'otp' => $otp,
            'otp_expiry' => $this->config['otp']['expiry'] / 60 // in minutes
        ]);
        
        return $this->sendEmail($email, $subject, $emailContent);
    }
    
    /**
     * Send welcome email
     */
    public function sendWelcomeEmail(string $email, string $name, array $userData): bool
    {
        $subject = 'Welcome to JobSpace!';
        
        $emailContent = $this->getWelcomeEmailTemplate([
            'name' => $name,
            'username' => $userData['username'],
            'role' => $userData['role'],
            'referral_code' => $userData['referral_code_generated'] ?? '',
            'login_url' => $this->getBaseUrl() . '/auth/login'
        ]);
        
        return $this->sendEmail($email, $subject, $emailContent);
    }
    
    /**
     * Send email using PHPMailer
     */
    private function sendEmail(string $to, string $subject, string $content): bool
    {
        // Always log email attempts for debugging
        error_log("=== EMAIL ATTEMPT ===");
        error_log("TO: $to");
        error_log("SUBJECT: $subject");
        error_log("FROM: " . $this->emailConfig['from_address']);
        
        // If email credentials are not configured, log instead
        if (empty($this->emailConfig['username']) || empty($this->emailConfig['password'])) {
            error_log("EMAIL CREDENTIALS NOT CONFIGURED - LOGGING ONLY");
            error_log("CONTENT: " . strip_tags($content));
            return true; // Return true for development
        }

        try {
            $mail = new PHPMailer(true);

            // Server settings
            $mail->isSMTP();
            $mail->Host = $this->emailConfig['host'];
            $mail->SMTPAuth = true;
            $mail->Username = $this->emailConfig['username'];
            $mail->Password = $this->emailConfig['password'];
            $mail->SMTPSecure = $this->emailConfig['encryption'] === 'tls' ? PHPMailer::ENCRYPTION_STARTTLS : PHPMailer::ENCRYPTION_SMTPS;
            $mail->Port = $this->emailConfig['port'];

            // Recipients
            $mail->setFrom($this->emailConfig['from_address'], $this->emailConfig['from_name']);
            $mail->addAddress($to);
            $mail->addReplyTo($this->emailConfig['from_address'], $this->emailConfig['from_name']);

            // Content
            $mail->isHTML(true);
            $mail->Subject = $subject;
            $mail->Body = $content;
            $mail->AltBody = strip_tags($content);

            // Send email
            $result = $mail->send();

            if ($result) {
                error_log("✅ Email sent successfully to: $to");
            } else {
                error_log("❌ Email sending failed but no exception thrown");
            }

            return $result;

        } catch (Exception $e) {
            error_log("❌ Email sending failed with exception:");
            error_log("PHPMailer Error: " . $mail->ErrorInfo);
            error_log("Exception Message: " . $e->getMessage());

            // Fallback: log the email content
            error_log("FALLBACK - EMAIL CONTENT:");
            error_log("CONTENT: " . strip_tags($content));

            return false;
        }
    }
    
    /**
     * Get verification email template
     */
    private function getVerificationEmailTemplate(array $data): string
    {
        return "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;'>
            <div style='text-align: center; margin-bottom: 30px;'>
                <h1 style='color: #2563eb; margin: 0;'>JobSpace</h1>
                <p style='color: #6b7280; margin: 5px 0;'>Professional Platform</p>
            </div>
            
            <div style='background: #f8fafc; padding: 30px; border-radius: 8px; margin-bottom: 20px;'>
                <h2 style='color: #1f2937; margin-top: 0;'>Verify Your Account</h2>
                <p style='color: #4b5563; line-height: 1.6;'>Hello {$data['name']},</p>
                <p style='color: #4b5563; line-height: 1.6;'>Thank you for registering with JobSpace! Please verify your email address using the OTP code below:</p>
                
                <div style='text-align: center; margin: 30px 0;'>
                    <div style='background: #2563eb; color: white; padding: 15px 30px; border-radius: 6px; font-size: 24px; font-weight: bold; letter-spacing: 3px; display: inline-block;'>
                        {$data['otp']}
                    </div>
                    <p style='color: #6b7280; font-size: 14px; margin-top: 10px;'>This code will expire in {$data['otp_expiry']} minutes</p>
                </div>
                
                <p style='color: #4b5563; line-height: 1.6;'>Alternatively, you can click the button below to verify your account:</p>
                
                <div style='text-align: center; margin: 20px 0;'>
                    <a href='{$data['verification_link']}' style='background: #10b981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;'>Verify Account</a>
                </div>
            </div>
            
            <div style='text-align: center; color: #6b7280; font-size: 14px;'>
                <p>If you didn't create an account, please ignore this email.</p>
                <p>&copy; 2024 JobSpace. All rights reserved.</p>
            </div>
        </div>";
    }
    
    /**
     * Get OTP email template
     */
    private function getOTPEmailTemplate(array $data): string
    {
        return "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;'>
            <div style='text-align: center; margin-bottom: 30px;'>
                <h1 style='color: #2563eb; margin: 0;'>JobSpace</h1>
                <p style='color: #6b7280; margin: 5px 0;'>Verification Code</p>
            </div>
            
            <div style='background: #f8fafc; padding: 30px; border-radius: 8px; margin-bottom: 20px;'>
                <h2 style='color: #1f2937; margin-top: 0;'>Your Verification Code</h2>
                <p style='color: #4b5563; line-height: 1.6;'>Hello {$data['name']},</p>
                <p style='color: #4b5563; line-height: 1.6;'>Here is your new verification code:</p>
                
                <div style='text-align: center; margin: 30px 0;'>
                    <div style='background: #2563eb; color: white; padding: 15px 30px; border-radius: 6px; font-size: 24px; font-weight: bold; letter-spacing: 3px; display: inline-block;'>
                        {$data['otp']}
                    </div>
                    <p style='color: #6b7280; font-size: 14px; margin-top: 10px;'>This code will expire in {$data['otp_expiry']} minutes</p>
                </div>
            </div>
            
            <div style='text-align: center; color: #6b7280; font-size: 14px;'>
                <p>If you didn't request this code, please ignore this email.</p>
                <p>&copy; 2024 JobSpace. All rights reserved.</p>
            </div>
        </div>";
    }
    
    /**
     * Get welcome email template
     */
    private function getWelcomeEmailTemplate(array $data): string
    {
        return "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;'>
            <div style='text-align: center; margin-bottom: 30px;'>
                <h1 style='color: #2563eb; margin: 0;'>JobSpace</h1>
                <p style='color: #6b7280; margin: 5px 0;'>Welcome to the Platform!</p>
            </div>
            
            <div style='background: #f0fdf4; padding: 30px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #10b981;'>
                <h2 style='color: #1f2937; margin-top: 0;'>🎉 Welcome to JobSpace!</h2>
                <p style='color: #4b5563; line-height: 1.6;'>Hello {$data['name']},</p>
                <p style='color: #4b5563; line-height: 1.6;'>Congratulations! Your account has been successfully created and verified.</p>
                
                <div style='background: white; padding: 20px; border-radius: 6px; margin: 20px 0;'>
                    <h3 style='color: #1f2937; margin-top: 0;'>Your Account Details:</h3>
                    <p style='margin: 5px 0;'><strong>Username:</strong> {$data['username']}</p>
                    <p style='margin: 5px 0;'><strong>Role:</strong> " . ucfirst($data['role']) . "</p>
                    <p style='margin: 5px 0;'><strong>Referral Code:</strong> {$data['referral_code']}</p>
                </div>
                
                <div style='text-align: center; margin: 20px 0;'>
                    <a href='{$data['login_url']}' style='background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;'>Login to Your Account</a>
                </div>
            </div>
            
            <div style='text-align: center; color: #6b7280; font-size: 14px;'>
                <p>Thank you for joining JobSpace!</p>
                <p>&copy; 2024 JobSpace. All rights reserved.</p>
            </div>
        </div>";
    }
    
    /**
     * Get base URL
     */
    private function getBaseUrl(): string
    {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        return $protocol . '://' . $host . '/jobspace';
    }
}

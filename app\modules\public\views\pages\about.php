<?php
$title = 'About - JobSpace';
$description = 'Learn more about JobSpace platform and its features';
include BASE_PATH . '/resources/views/components/header/public-header.php';
?>

<!-- About Content -->
<div class="py-12">
    <div class="max-w-4xl mx-auto px-4">
        <div class="bg-white rounded-lg shadow-lg p-8">
            <h1 class="text-4xl font-bold text-gray-800 mb-6">About JobSpace</h1>
            
            <div class="grid md:grid-cols-2 gap-8 mb-8">
                <div>
                    <h2 class="text-2xl font-semibold text-gray-700 mb-4">🎯 Platform Features</h2>
                    <ul class="space-y-2 text-gray-600">
                        <li>• Quiz System (35 features)</li>
                        <li>• Social Media Platform (20 features)</li>
                        <li>• E-commerce System (15 features)</li>
                        <li>• Freelancing Marketplace (20 features)</li>
                        <li>• Wallet & Payment System</li>
                        <li>• Unified Feed System</li>
                        <li>• Real-time Notifications</li>
                    </ul>
                </div>
                
                <div>
                    <h2 class="text-2xl font-semibold text-gray-700 mb-4">⚡ Performance</h2>
                    <ul class="space-y-2 text-gray-600">
                        <li>• Supports 50K+ concurrent users</li>
                        <li>• <200ms response time</li>
                        <li>• 99.9% uptime target</li>
                        <li>• Advanced caching system</li>
                        <li>• Database optimization</li>
                        <li>• Horizontal scaling ready</li>
                    </ul>
                </div>
            </div>
            
            <div class="mb-8">
                <h2 class="text-2xl font-semibold text-gray-700 mb-4">🏗️ Architecture</h2>
                <p class="text-gray-600 mb-4">
                    JobSpace is built with a modular architecture that allows each component to work independently 
                    while seamlessly integrating with other modules. The platform uses microservices-based design 
                    patterns for maximum scalability and maintainability.
                </p>
                <div class="bg-gray-50 p-4 rounded">
                    <h3 class="font-semibold text-gray-700 mb-2">Core Technologies:</h3>
                    <p class="text-sm text-gray-600">
                        Pure PHP 8+, MariaDB with InnoDB, Custom MVC Framework, 
                        File-based Smart Caching, Tailwind CSS, Apache/Nginx
                    </p>
                </div>
            </div>
            
            <div class="text-center">
                <a href="/jobspace/contact" class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition mr-4">
                    Contact Us
                </a>
                <a href="/jobspace/" class="bg-gray-500 text-white px-6 py-3 rounded-lg hover:bg-gray-600 transition">
                    Back to Home
                </a>
            </div>
        </div>
    </div>
</div>

<?php include BASE_PATH . '/resources/views/components/footer/public-footer.php';?>

<?php 
$title = "Login | JobSpace";
$bodyClass = "auth-container";
include BASE_PATH . '/resources/views/components/header/auth-header.php'; 
?>
    <div class="min-h-screen flex items-center justify-center p-4">
        <div class="bg-white rounded-2xl shadow-2xl w-full max-w-md">
            <!-- Header -->
            <div class="p-8 pb-6">
                <div class="text-center mb-8">
                    <div class="text-4xl mb-4">🚀</div>
                    <h1 class="text-3xl font-bold text-gray-800 mb-2">Welcome Back!</h1>
                    <p class="text-gray-600">Sign in to your JobSpace account</p>
                </div>

                <!-- Success/Error Messages -->
                <?php if (isset($_SESSION['success_message'])): ?>
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                        <div class="flex items-center">
                            <span class="text-green-500 text-xl mr-2">✅</span>
                            <p class="text-green-700"><?= htmlspecialchars($_SESSION['success_message']) ?></p>
                        </div>
                    </div>
                    <?php unset($_SESSION['success_message']); ?>
                <?php endif; ?>

                <?php if (isset($_SESSION['login_error'])): ?>
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                        <div class="flex items-center">
                            <span class="text-red-500 text-xl mr-2">❌</span>
                            <p class="text-red-700"><?= htmlspecialchars($_SESSION['login_error']) ?></p>
                        </div>
                    </div>
                    <?php unset($_SESSION['login_error']); ?>
                <?php endif; ?>
            </div>

            <!-- Login Form -->
            <form action="/jobspace/auth/process-login" method="POST" class="px-8 pb-8" id="loginForm">
                <?= \App\Core\Security::getCSRFField() ?>
                <!-- Email/Username -->
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-semibold mb-2">Email or Username</label>
                    <input type="text" name="login" id="login" required
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 transition"
                           placeholder="Enter your email or username"
                           value="<?= htmlspecialchars($_SESSION['login_data']['login'] ?? '') ?>">
                </div>

                <!-- Password -->
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-semibold mb-2">Password</label>
                    <div class="relative">
                        <input type="password" name="password" id="password" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 transition pr-12"
                               placeholder="Enter your password">
                        <button type="button" id="togglePassword" 
                                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700">
                            <span id="eyeIcon">👁️</span>
                        </button>
                    </div>
                </div>

                <!-- Remember Me & Forgot Password -->
                <div class="flex items-center justify-between mb-6">
                    <label class="flex items-center">
                        <input type="checkbox" name="remember_me" value="1"
                               class="w-4 h-4 text-blue-500 border-gray-300 rounded focus:ring-blue-500"
                               <?= ($_SESSION['login_data']['remember_me'] ?? '') ? 'checked' : '' ?>>
                        <span class="ml-2 text-sm text-gray-700">Remember me</span>
                    </label>
                    <a href="/jobspace/auth/forgot-password" 
                       class="text-sm text-blue-500 hover:text-blue-600 font-semibold">
                        Forgot Password?
                    </a>
                </div>

                <!-- Login Button -->
                <button type="submit" 
                        class="w-full bg-blue-500 text-white py-3 rounded-lg font-semibold hover:bg-blue-600 transition duration-200">
                    Sign In
                </button>

                <!-- Divider -->
                <div class="flex items-center my-6">
                    <div class="flex-1 border-t border-gray-300"></div>
                    <span class="px-4 text-gray-500 text-sm">or</span>
                    <div class="flex-1 border-t border-gray-300"></div>
                </div>

                <!-- Social Login (Placeholder) -->
                <div class="space-y-3">
                    <button type="button" 
                            class="w-full bg-red-500 text-white py-3 rounded-lg font-semibold hover:bg-red-600 transition duration-200 flex items-center justify-center">
                        <span class="mr-2">🔴</span>
                        Continue with Google
                    </button>
                    <button type="button" 
                            class="w-full bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition duration-200 flex items-center justify-center">
                        <span class="mr-2">📘</span>
                        Continue with Facebook
                    </button>
                </div>

                <!-- Register Link -->
                <div class="text-center mt-8">
                    <p class="text-gray-600">Don't have an account? 
                        <a href="/jobspace/auth/register" class="text-blue-500 hover:text-blue-600 font-semibold">Create one here</a>
                    </p>
                </div>
            </form>

            <!-- Footer -->
            <div class="px-8 pb-6 text-center">
                <p class="text-xs text-gray-500">
                    By signing in, you agree to our 
                    <a href="/jobspace/terms" class="text-blue-500 hover:text-blue-600">Terms</a> and 
                    <a href="/jobspace/privacy" class="text-blue-500 hover:text-blue-600">Privacy Policy</a>
                </p>
            </div>
        </div>
    </div>

    <script>
        // Password visibility toggle
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const eyeIcon = document.getElementById('eyeIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                eyeIcon.textContent = '🙈';
            } else {
                passwordInput.type = 'password';
                eyeIcon.textContent = '👁️';
            }
        });

        // Form submission
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.disabled = true;
            submitBtn.textContent = 'Signing In...';
            submitBtn.className = 'w-full bg-blue-400 text-white py-3 rounded-lg font-semibold cursor-not-allowed';
        });

        // Auto-focus login field
        document.getElementById('login').focus();

        // Social login placeholders
        document.querySelectorAll('button[type="button"]').forEach(btn => {
            if (btn.id !== 'togglePassword') {
                btn.addEventListener('click', function() {
                    alert('🚧 Social login coming soon! Please use email/username login for now.');
                });
            }
        });
    </script>

<?php
include BASE_PATH . '/resources/views/components/footer/auth-footer.php';
?>

<?php
// Clear session data after displaying
unset($_SESSION['login_data']);
?>

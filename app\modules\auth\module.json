{"name": "<PERSON><PERSON>", "version": "1.0.0", "description": "Authentication module with email verification, OTP, and multi-step registration", "author": "JobSpace Team", "type": "auth", "status": "active", "dependencies": [], "routes": {"web": "routes/web.php"}, "config": {"email_verification": true, "otp_verification": true, "dual_verification": true, "session_timeout": 3600, "password_reset": true, "multi_step_registration": true}, "features": ["multi_step_registration", "email_verification", "otp_verification", "password_reset", "welcome_email", "role_based_access", "referral_system", "profile_picture_upload"], "roles": ["User", "Business", "Creator", "Admin"]}
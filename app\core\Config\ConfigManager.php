<?php

namespace App\Core\Config;

use Exception;

/**
 * Configuration Manager
 * Handles application configuration with caching
 */
class ConfigManager
{
    private array $config = [];
    private array $cache = [];
    private bool $loaded = false;
    private string $configPath;

    public function __construct(string $configPath = null)
    {
        $this->configPath = $configPath ?: BASE_PATH . '/config';
    }

    /**
     * Load all configuration files
     */
    public function load(): void
    {
        if ($this->loaded) {
            return;
        }

        // Load environment variables first
        $this->loadEnvironment();

        // Load configuration files
        $this->loadConfigFiles();

        $this->loaded = true;
    }

    /**
     * Load environment variables
     */
    private function loadEnvironment(): void
    {
        $envFile = BASE_PATH . '/.env';
        
        if (file_exists($envFile)) {
            $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            
            foreach ($lines as $line) {
                if (strpos($line, '#') === 0) {
                    continue; // Skip comments
                }
                
                if (strpos($line, '=') !== false) {
                    [$key, $value] = explode('=', $line, 2);
                    $key = trim($key);
                    $value = trim($value, '"\'');
                    
                    if (!array_key_exists($key, $_ENV)) {
                        $_ENV[$key] = $value;
                        putenv("{$key}={$value}");
                    }
                }
            }
        }
    }

    /**
     * Load configuration files
     */
    private function loadConfigFiles(): void
    {
        if (!is_dir($this->configPath)) {
            return;
        }

        $files = glob($this->configPath . '/*.php');
        
        foreach ($files as $file) {
            $key = basename($file, '.php');
            $this->config[$key] = require $file;
        }

        // Set default configurations
        $this->setDefaults();
    }

    /**
     * Set default configuration values
     */
    private function setDefaults(): void
    {
        // App defaults
        $this->config['app'] = array_merge([
            'name' => 'JobSpace',
            'env' => 'production',
            'debug' => false,
            'url' => 'http://localhost',
            'timezone' => 'UTC',
            'locale' => 'en',
            'key' => null,
            'cipher' => 'AES-256-CBC'
        ], $this->config['app'] ?? []);

        // Database defaults
        $this->config['database'] = array_merge([
            'default' => 'mysql',
            'connections' => [
                'mysql' => [
                    'driver' => 'mysql',
                    'host' => env('DB_HOST', '127.0.0.1'),
                    'port' => env('DB_PORT', '3306'),
                    'database' => env('DB_DATABASE', 'jobspace'),
                    'username' => env('DB_USERNAME', 'root'),
                    'password' => env('DB_PASSWORD', ''),
                    'charset' => 'utf8mb4',
                    'collation' => 'utf8mb4_unicode_ci',
                    'prefix' => '',
                    'strict' => true,
                    'engine' => 'InnoDB',
                    'persistent' => false
                ]
            ],
            'max_connections' => 100,
            'timeout' => 30
        ], $this->config['database'] ?? []);

        // Cache defaults
        $this->config['cache'] = array_merge([
            'default' => 'file',
            'stores' => [
                'file' => [
                    'driver' => 'file',
                    'path' => BASE_PATH . '/storage/cache'
                ]
            ],
            'prefix' => 'jobspace_cache',
            'ttl' => 3600
        ], $this->config['cache'] ?? []);

        // Session defaults
        $this->config['session'] = array_merge([
            'driver' => 'file',
            'lifetime' => 120,
            'expire_on_close' => false,
            'encrypt' => false,
            'files' => BASE_PATH . '/storage/sessions',
            'connection' => null,
            'table' => 'sessions',
            'store' => null,
            'lottery' => [2, 100],
            'cookie' => 'jobspace_session',
            'path' => '/',
            'domain' => null,
            'secure' => false,
            'http_only' => true,
            'same_site' => 'lax'
        ], $this->config['session'] ?? []);

        // Security defaults
        $this->config['security'] = array_merge([
            'csrf_token_name' => '_token',
            'csrf_header_name' => 'X-CSRF-TOKEN',
            'rate_limit' => [
                'enabled' => true,
                'max_attempts' => 60,
                'decay_minutes' => 1
            ],
            'password' => [
                'min_length' => 8,
                'require_uppercase' => true,
                'require_lowercase' => true,
                'require_numbers' => true,
                'require_symbols' => false
            ]
        ], $this->config['security'] ?? []);
    }

    /**
     * Get configuration value
     */
    public function get(string $key, $default = null)
    {
        if (isset($this->cache[$key])) {
            return $this->cache[$key];
        }

        $value = $this->getValue($key, $default);
        $this->cache[$key] = $value;

        return $value;
    }

    /**
     * Set configuration value
     */
    public function set(string $key, $value): void
    {
        $this->setValue($key, $value);
        $this->cache[$key] = $value;
    }

    /**
     * Check if configuration key exists
     */
    public function has(string $key): bool
    {
        return $this->getValue($key) !== null;
    }

    /**
     * Get all configuration
     */
    public function all(): array
    {
        return $this->config;
    }

    /**
     * Get configuration for a specific file
     */
    public function getFile(string $file): array
    {
        return $this->config[$file] ?? [];
    }

    /**
     * Get value using dot notation
     */
    private function getValue(string $key, $default = null)
    {
        $keys = explode('.', $key);
        $value = $this->config;

        foreach ($keys as $segment) {
            if (is_array($value) && array_key_exists($segment, $value)) {
                $value = $value[$segment];
            } else {
                return $default;
            }
        }

        return $value;
    }

    /**
     * Set value using dot notation
     */
    private function setValue(string $key, $value): void
    {
        $keys = explode('.', $key);
        $config = &$this->config;

        foreach ($keys as $i => $segment) {
            if (count($keys) === 1) {
                break;
            }

            unset($keys[$i]);

            if (!isset($config[$segment]) || !is_array($config[$segment])) {
                $config[$segment] = [];
            }

            $config = &$config[$segment];
        }

        $config[array_shift($keys)] = $value;
    }

    /**
     * Clear configuration cache
     */
    public function clearCache(): void
    {
        $this->cache = [];
    }

    /**
     * Reload configuration
     */
    public function reload(): void
    {
        $this->config = [];
        $this->cache = [];
        $this->loaded = false;
        $this->load();
    }

    /**
     * Get environment variable with fallback
     */
    public function env(string $key, $default = null)
    {
        $value = $_ENV[$key] ?? getenv($key);
        
        if ($value === false) {
            return $default;
        }

        // Convert string booleans
        if (in_array(strtolower($value), ['true', 'false'])) {
            return strtolower($value) === 'true';
        }

        // Convert string nulls
        if (strtolower($value) === 'null') {
            return null;
        }

        // Convert numeric strings
        if (is_numeric($value)) {
            return strpos($value, '.') !== false ? (float) $value : (int) $value;
        }

        return $value;
    }

    /**
     * Validate configuration
     */
    public function validate(): array
    {
        $errors = [];

        // Validate required configurations
        $required = [
            'app.key' => 'Application key is required',
            'database.connections.mysql.host' => 'Database host is required',
            'database.connections.mysql.database' => 'Database name is required'
        ];

        foreach ($required as $key => $message) {
            if (!$this->has($key) || empty($this->get($key))) {
                $errors[] = $message;
            }
        }

        return $errors;
    }

    /**
     * Export configuration to array
     */
    public function export(): array
    {
        return [
            'config' => $this->config,
            'cache' => $this->cache,
            'loaded' => $this->loaded
        ];
    }
}

/**
 * Helper function to get environment variable
 */
if (!function_exists('env')) {
    function env(string $key, $default = null) {
        $value = $_ENV[$key] ?? getenv($key);
        
        if ($value === false) {
            return $default;
        }

        // Convert string booleans
        if (in_array(strtolower($value), ['true', 'false'])) {
            return strtolower($value) === 'true';
        }

        // Convert string nulls
        if (strtolower($value) === 'null') {
            return null;
        }

        // Convert numeric strings
        if (is_numeric($value)) {
            return strpos($value, '.') !== false ? (float) $value : (int) $value;
        }

        return $value;
    }
}
